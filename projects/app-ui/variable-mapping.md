# CSS 变量重构映射表

## 颜色变量映射

### 主色调
- `--primary-color` → `--md-sys-color-primary`
- `--primary-light-color` → `--app-primary-light-color` (已存在)
- `--text-on-primary` → `--md-sys-color-on-primary`

### 文本颜色
- `--text-primary-color` → `--md-sys-color-on-surface`
- `--text-secondary-color` → `--md-sys-color-on-surface-variant`

### 表面和背景色
- `--surface-color` → `--md-sys-color-surface`
- `--surface-variant-color` → `--md-sys-color-surface-variant`
- `--background-color` → `--md-sys-color-background`
- `--background-alt-color` → `--app-background-alt-color` (已存在)

### 状态颜色
- `--error-color` → `--md-sys-color-error`
- `--error-lighter-color` → `--md-sys-color-error-container`
- `--success-color` → `--md-sys-color-success`
- `--success-lighter-color` → `--md-sys-color-success-container`
- `--warning-color` → `--md-sys-color-warning`

### 边框和分割线
- `--divider-color` → `--md-sys-color-outline`
- `--border-color` → `--md-sys-color-outline`

## 间距变量映射

### 间距
- `--spacing-xs` → `--md-sys-spacing-1` (4px)
- `--spacing-sm` → `--md-sys-spacing-2` (8px)
- `--spacing-md` → `--md-sys-spacing-4` (16px)
- `--spacing-lg` → `--md-sys-spacing-6` (24px)
- `--spacing-xl` → `--md-sys-spacing-8` (32px)

## 形状变量映射

### 圆角
- `--border-radius-sm` → `--md-sys-shape-corner-small`
- `--border-radius-md` → `--md-sys-shape-corner-medium`
- `--border-radius-lg` → `--md-sys-shape-corner-large`
- `--border-radius-full` → `--md-sys-shape-corner-full`

## 字体变量映射

### 字体大小
- `--font-size-xs` → `--md-sys-typescale-label-small-size`
- `--font-size-sm` → `--md-sys-typescale-label-large-size`
- `--font-size-md` → `--md-sys-typescale-body-medium-size`
- `--font-size-lg` → `--md-sys-typescale-title-medium-size`
- `--font-size-xl` → `--md-sys-typescale-headline-small-size`

## 动画变量映射

### 过渡时间
- `--transition-fast` → `--md-sys-motion-duration-short2`
- `--transition-normal` → `--md-sys-motion-duration-medium2`
- `--transition-slow` → `--md-sys-motion-duration-long2`

## 布局变量映射

### 工具栏和布局
- `--toolbar-height` → `--app-toolbar-height` (已存在)
- `--sidebar-width` → `--md-sys-layout-sidebar-width`
- `--header-height` → `--md-sys-layout-header-height`

## 需要删除的变量
以下变量将被删除，因为它们可以用现有的 Material Design 变量替代：
- 所有自定义的颜色变量（除了 --app- 前缀的特殊用途变量）
- 所有自定义的间距变量
- 所有自定义的字体变量
- 所有自定义的形状变量
