import {Routes} from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'chat',
  },
  {
    path: 'home',
    loadComponent: () =>
        import('./features/home/<USER>').then(
            (m) => m.HomeComponent,
        ),
  },
  {
    path: 'chat',
    loadComponent: () =>
        import('./features/chat/chat-container/chat-container.component').then(
            (m) => m.ChatContainerComponent,
        ),
  },
  {
    path: 'chat/:sessionId',
    loadComponent: () =>
        import('./features/chat/chat-container/chat-container.component').then(
            (m) => m.ChatContainerComponent,
        ),
  },
  {
    path: 'dashboard',
    loadComponent: () =>
        import('./features/dashboard/dashboard.component').then(
            (m) => m.DashboardComponent,
        ),
  },
  {
    path: 'profile',
    loadComponent: () =>
        import('./features/profile/profile.component').then(
            (m) => m.ProfileComponent,
        ),
  },
  {
    path: 'messages',
    loadComponent: () =>
        import('./features/messages/messages.component').then(
            (m) => m.MessagesComponent,
        ),
  },
  {
    path: 'favorites',
    loadComponent: () =>
        import('./features/favorites/favorites.component').then(
            (m) => m.FavoritesComponent,
        ),
  },
  {
    path: 'settings',
    loadComponent: () =>
        import('./features/settings/settings.component').then(
            (m) => m.SettingsComponent,
        ),
  },
  {
    path: 'help',
    loadComponent: () =>
        import('./features/help/help.component').then(
            (m) => m.HelpComponent,
        ),
  },
  {
    path: 'about',
    loadComponent: () =>
        import('./features/about/about.component').then(
            (m) => m.AboutComponent,
        ),
  },
  {
    path: 'contact',
    loadComponent: () =>
        import('./features/contact/contact.component').then(
            (m) => m.ContactComponent,
        ),
  },
  {
    path: 'login',
    loadComponent: () =>
        import('./auth/login/login.component').then(
            (m) => m.LoginComponent,
        ),
  },
  {
    path: 'register',
    loadComponent: () =>
        import('./auth/register/register.component').then(
            (m) => m.RegisterComponent,
        ),
  },
  {
    path: 'reset-password',
    loadComponent: () =>
        import('./auth/reset-password/reset-password.component').then(
            (m) => m.ResetPasswordComponent,
        ),
  },
  {
    path: 'terms',
    loadComponent: () =>
        import('./features/terms/terms.component').then(
            (m) => m.TermsComponent,
        ),
  },
  {
    path: 'privacy',
    loadComponent: () =>
        import('./features/privacy/privacy.component').then(
            (m) => m.PrivacyComponent,
        ),
  },
];
