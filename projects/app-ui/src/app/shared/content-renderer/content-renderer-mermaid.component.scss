.content-renderer-mermaid {
  .mermaid-container {

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--md-sys-spacing-1);
    overflow: auto;
    min-height: 100px;
  }

  .mermaid-loading {
    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-body-small-size);
    padding: var(--md-sys-spacing-4);
    text-align: center;
  }

  .mermaid-error {
    color: var(--md-sys-color-error);
    background-color: var(--md-sys-color-error-container);
    border: 1px solid var(--md-sys-color-error);
    border-radius: var(--md-sys-shape-corner-small);
    padding: var(--md-sys-spacing-4);
    margin: var(--md-sys-spacing-4) 0;
    font-size: var(--md-sys-typescale-body-small-size);

    max-width: 600px;

    .error-title {
      font-weight: bold;
      margin-bottom: var(--md-sys-spacing-1);
    }

    .error-message {
      font-family: var(--md-sys-typescale-font-family-monospace);
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  svg {
    max-
    border-radius: var(--md-sys-shape-corner-small);
  }

  &.fullscreen {
    .mermaid-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      svg {
        max-
        max-height: 90vh;
      }
    }
  }
}
