.content-renderer-video {
  .video-container {

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--md-sys-spacing-1);
  }

  video {
    max-

    border-radius: var(--md-sys-shape-corner-small);
    box-shadow: var(--md-sys-elevation-level1);
  }

  &.fullscreen {
    .video-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      video {
        max-
        max-height: 90vh;
      }
    }
  }
}
