.content-renderer-image {
  .image-container {

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--md-sys-spacing-1);
  }

  img {
    max-

    border-radius: var(--md-sys-shape-corner-small);
    box-shadow: var(--md-sys-elevation-level1);
  }

  &.fullscreen {
    .image-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        max-
        max-height: 100vh;
        object-fit: contain;
      }
    }
  }
}
