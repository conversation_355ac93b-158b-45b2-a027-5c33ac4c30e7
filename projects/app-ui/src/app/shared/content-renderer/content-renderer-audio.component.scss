.content-renderer-audio {
  .audio-container {

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--md-sys-spacing-1);
  }

  audio {

    border-radius: var(--md-sys-shape-corner-small);
  }

  &.fullscreen {
    .audio-container {
      max-width: 90%;
      width: 600px;
      overflow: auto;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      audio {

      }
    }
  }
}
