.content-renderer-code {
  .code-container {
    position: relative;

    overflow-x: auto;
  }

  .code-language {
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--md-sys-spacing-1) var(--md-sys-spacing-1);
    font-size: var(--md-sys-typescale-label-small-size);
    color: var(--md-sys-color-on-surface-variant);
    background-color: var(--md-sys-color-secondary-container);
    border-bottom-left-radius: var(--md-sys-shape-corner-small);
    z-index: 1;
  }

  pre {

    padding-top: var(--md-sys-spacing-6) !important;
  }

  &.fullscreen {
    .code-container {
      max-width: 90%;
      max-height: 90%;
      overflow: auto;
      flex: 1;
    }
  }
}
