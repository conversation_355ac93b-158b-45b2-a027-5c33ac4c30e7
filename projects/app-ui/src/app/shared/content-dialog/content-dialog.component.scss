.content-dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;

  max-width: 90vw;
  overflow: hidden;
  position: relative; /* 添加相对定位，使子元素可以正确定位 */
}

.content-dialog-header {
  display: flex;
  justify-content: flex-end;
  padding: var(--md-sys-spacing-1);
  border-bottom: 1px solid var(--md-sys-color-outline);
}

.content-dialog-body {
  flex: 1;
  overflow: auto;
  padding: var(--md-sys-spacing-4);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐，避免代码块在垂直方向居中 */
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  height: 100%;
  overflow: auto;
}

.content-dialog-footer {
  display: flex;
  justify-content: center;
  padding: var(--md-sys-spacing-1);
  border-top: 1px solid var(--md-sys-color-outline);
  background-color: var(--md-sys-color-surface);
}

.content-dialog-toolbar {
  display: flex;
  justify-content: center;
  padding: var(--md-sys-spacing-1);
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-small);

  .toolbar-button {
    margin: 0 var(--md-sys-spacing-1);
  }
}

// 代码内容样式
.code-content {

  max-
  overflow: auto;
  align-self: flex-start;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-small);

  margin-bottom: var(--md-sys-spacing-4);

  pre {

    overflow: auto;
    padding: var(--md-sys-spacing-4);
    max-height: calc(90vh - 150px); /* 限制高度，留出空间给工具栏 */
    code {

    }
  }
}

// 图片内容样式
.image-content {
  display: flex;
  justify-content: center;
  align-items: center;

  height: 100%;

  img {
    max-
    max-height: 100%;
    object-fit: contain;
  }
}

// 视频内容样式
.video-content {
  display: flex;
  justify-content: center;
  align-items: center;

  height: 100%;

  video {
    max-
    max-height: 100%;
  }
}

// 音频内容样式
.audio-content {
  display: flex;
  justify-content: center;
  align-items: center;

  audio {

    max-width: 600px;
  }
}

// Mermaid 和 PlantUML 内容样式
.mermaid-content,
.plantuml-content {
  display: flex;
  justify-content: center;
  align-items: center;

  height: 100%;
  overflow: auto;

  svg, img {
    max-
    max-height: 100%;
  }
}

// 默认内容样式
.default-content {

  height: 100%;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-word;
}
