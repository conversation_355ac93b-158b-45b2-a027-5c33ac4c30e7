app-markdown > .markdown-content {
  line-height: var(--md-sys-typescale-line-height-normal);

  h1, h2, h3, h4, h5, h6 {
    margin-top: var(--md-sys-spacing-6);
    margin-bottom: var(--md-sys-spacing-2);
    font-weight: var(--md-sys-typescale-font-weight-medium);
  }

  h1 {
    font-size: var(--md-sys-typescale-title-large-size);
  }

  h2 {
    font-size: var(--md-sys-typescale-title-medium-size);
  }

  h3 {
    font-size: var(--md-sys-typescale-body-medium-size);
  }

  p {
    margin: var(--md-sys-spacing-4) 0;
  }

  ul, ol {
    margin: var(--md-sys-spacing-4) 0;
    padding-left: var(--md-sys-spacing-4);
  }

  li {
    margin: var(--md-sys-spacing-1) 0;
  }

  a {
    color: var(--md-sys-color-primary);
    text-decoration: none;
    transition: color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      color: var(--md-sys-color-on-primary-container);
      text-decoration: underline;
    }
  }

  blockquote {
    margin: var(--md-sys-spacing-4) 0;
    padding-left: var(--md-sys-spacing-4);
    border-left: var(--border-width-thick) solid var(--md-sys-color-outline);
    color: var(--text-secondary);
  }

  code {
    background-color: var(--secondary-color);
    padding: var(--spacing-xxs) var(--md-sys-spacing-1);
    border-radius: var(--md-sys-shape-corner-small);
    font-size: var(--md-sys-typescale-label-large-size);
  }

  pre {
    margin: var(--md-sys-spacing-4) 0;
    padding: var(--md-sys-spacing-4);
    background-color: var(--secondary-color);
    border-radius: var(--md-sys-shape-corner-medium);
    overflow-x: auto;

    code {
      background-color: transparent;

      border-radius: 0;
      font-size: var(--md-sys-typescale-label-large-size);
    }
  }

  img {
    border-radius: var(--md-sys-shape-corner-small);
  }

  video, audio {
    display: block;
    margin: var(--md-sys-spacing-4) 0;
    border-radius: var(--md-sys-shape-corner-small);
    background-color: var(--md-sys-color-secondary-container);
  }

  hr {
    margin: var(--md-sys-spacing-8) 0;
    border: none;
    border-top: 1px solid var(--md-sys-color-outline);
  }

  [class^="content-renderer-"] {
    position: relative;
    /* 工具栏基础样式 */
    .toolbar {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--md-sys-spacing-1);
      margin-top: calc(-1 * var(--md-sys-spacing-1));
      opacity: 0.5;
      transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

      /* 默认状态下工具栏透明度较低，悬停时恢复 */
      &:hover {
        opacity: 1;
      }
    }

    /* 组件悬停时工具栏完全显示 */
    &:hover .toolbar {
      opacity: 1;
    }

    /* 工具按钮基础样式 */
    .toolbar-button {
      width: 24px;
      height: 24px;
      line-height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--md-sys-color-background);
      border: 1px solid var(--md-sys-color-outline);
      box-shadow: var(--md-sys-elevation-level1);
      border-radius: 50%;
      transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

      min-width: 0;
      position: relative;

      &:hover {
        background-color: var(--md-sys-color-secondary-container);
        box-shadow: var(--md-sys-elevation-level2);
      }

      /* 图标样式 */
      mat-icon {
        --mdc-icon-button-icon-size: 12px;
        font-size: 12px;
        width: 12px;
        height: 12px;
      }
    }

    /* 全屏模式样式覆盖 */
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 1000;

      padding: var(--md-sys-spacing-6);
      background-color: var(--md-sys-color-background);
      border-radius: 0;
      border: none;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      /* 全屏模式下工具栏位置和样式覆盖 */
      .toolbar {
        opacity: 1;
      }
    }
  }
}
