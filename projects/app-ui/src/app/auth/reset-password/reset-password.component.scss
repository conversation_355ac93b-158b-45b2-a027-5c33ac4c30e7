.reset-password-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 16px;
  min-height: calc(100vh - 64px); // 减去顶部导航栏高度
}

.reset-password-card {
  background-color: var(--md-sys-color-surface);
  border-radius: 8px;
  box-shadow: var(--md-sys-elevation-level2);
  padding: 32px;

  max-width: 480px;
}

.card-header {
  text-align: center;
  margin-bottom: 32px;

  .header-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    h1 {

      font-size: 28px;
      font-weight: 500;
    }

    mat-icon {
      font-size: 28px;
      color: var(--md-sys-color-primary);
    }
  }

  .header-subtitle {

    color: var(--md-sys-color-on-surface-variant);
    font-size: 16px;
  }
}

.reset-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.error-message {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-error);
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;

  button[type="submit"] {
    min-width: 120px;
    display: flex;
    align-items: center;
    gap: 8px;

    mat-spinner {
      margin-right: 8px;
    }
  }
}

.success-message {
  background-color: var(--md-sys-color-success-container);
  color: var(--md-sys-color-success);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 24px;
  text-align: center;

  p {
    margin: 8px 0;

  }
}

.success-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

// 响应式调整
@media (max-width: 599px) {
  .reset-password-card {
    padding: 24px 16px;
  }

  .card-header {
    margin-bottom: 24px;

    .header-title {
      h1 {
        font-size: 24px;
      }

      mat-icon {
        font-size: 24px;
      }
    }
  }
}
