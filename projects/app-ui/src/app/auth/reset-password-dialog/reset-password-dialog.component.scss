.reset-password-dialog {
  padding: 24px;
  max-width: 400px;

  box-sizing: border-box; // 确保padding不会增加总宽度
  margin: 0 auto; // 居中对齐
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.dialog-header {
  text-align: center;
  margin-bottom: 24px;

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;

    h2 {

      font-size: 24px;
      font-weight: 500;
    }
  }

  .dialog-subtitle {

    color: var(--md-sys-color-on-surface-variant);
    font-size: 14px;
  }
}

.reset-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {

  margin-bottom: 0; // 覆盖全局样式中的margin-bottom

  // 确保输入框内部的padding是对称的
  ::ng-deep .mat-mdc-form-field-infix {
    padding-left: 0;
    padding-right: 0;
  }
}

.error-message {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-error);
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px; // 减小按钮之间的间距
  margin-top: 16px;
  flex-wrap: wrap; // 在小屏幕上允许换行

  button {
    min-width: 70px; // 减小最小宽度

  }

  button[type="submit"] {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-spinner {
      margin-right: 8px;
    }

    mat-icon {
      font-size: 18px;
    }
  }

  // 在小屏幕上调整按钮
  @media (max-width: 320px) {
    justify-content: center; // 在非常小的屏幕上居中显示

    button {
      flex: 1 1 auto; // 允许按钮伸缩
    }
  }
}

.success-message {
  background-color: var(--md-sys-color-success-container);
  color: var(--md-sys-color-success);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  text-align: center;

  p {
    margin: 8px 0;

    font-size: 14px;
  }
}

.success-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

// 覆盖Material Dialog样式
:host {
  display: block;

  ::ng-deep .mat-mdc-dialog-container {
    padding: 0 !important;

    .mdc-dialog__surface {
      padding: 0 !important; // 移除对话框表面的内边距
    }

    .mat-mdc-dialog-surface {
      padding: 0 !important;
    }

    .mat-mdc-dialog-content {
      padding: 0 !important; // 移除对话框内容的内边距
      margin: 0 !important;
      display: block !important;
    }
  }
}

// 响应式调整
@media (max-width: 400px) {
  .reset-password-dialog {
    padding: 16px; // 减小但保持对称的内边距
  }

  .dialog-header {
    margin-bottom: 16px; // 减小底部间距

    .dialog-title {
      h2 {
        font-size: 20px; // 减小标题字体大小
      }
    }
  }

  .reset-form {
    gap: 12px; // 减小表单元素间距
  }
}
