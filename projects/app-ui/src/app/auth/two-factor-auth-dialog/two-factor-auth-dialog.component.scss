.two-factor-auth-dialog {
  padding: var(--md-sys-spacing-6);
  max-width: 500px;
}

.dialog-header {
  text-align: center;
  margin-bottom: var(--md-sys-spacing-6);

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--md-sys-spacing-2);
    margin-bottom: var(--md-sys-spacing-2);

    mat-icon {
      color: var(--md-sys-color-primary);
    }
  }

  .dialog-subtitle {
    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-label-large-size);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-message {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-error);
  padding: var(--md-sys-spacing-3);
  border-radius: var(--md-sys-shape-corner-extra-small);
  font-size: var(--md-sys-typescale-label-large-size);
  margin-bottom: var(--md-sys-spacing-4);
}

.two-factor-content {
  margin-bottom: var(--md-sys-spacing-6);
}

.status-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-6);
  padding: var(--md-sys-spacing-4);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-small);

  .status-info p {
    color: var(--md-sys-color-on-surface-variant);
  }
}

.step-content {
  padding: var(--md-sys-spacing-4) 0;

  .hint {
    font-size: var(--md-sys-typescale-label-large-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  .warning {
    color: var(--md-sys-color-warning);
    font-weight: var(--md-sys-typescale-font-weight-medium);
  }
}

.qr-code {
  display: flex;
  justify-content: center;
  margin: var(--md-sys-spacing-4) 0;

  img {
    max-width: 200px;
    border: 1px solid var(--md-sys-color-outline);
    padding: var(--md-sys-spacing-2);
    background-color: var(--md-sys-color-surface);
  }
}

.recovery-codes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--md-sys-spacing-2);
  margin: var(--md-sys-spacing-4) 0;
  padding: var(--md-sys-spacing-4);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);

  .recovery-code {
    font-family: var(--md-sys-typescale-font-family-monospace);
    padding: var(--md-sys-spacing-2);
    background-color: var(--md-sys-color-surface);
    border: 1px solid var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-extra-small);
    text-align: center;
  }
}

.step-actions {
  display: flex;
  justify-content: space-between;
  margin-top: var(--md-sys-spacing-4);
}

.enabled-info {
  padding: var(--md-sys-spacing-4);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-small);
  margin-bottom: var(--md-sys-spacing-4);
}

.success-message {
  text-align: center;
  padding: var(--md-sys-spacing-6) var(--md-sys-spacing-4);

  mat-icon {
    font-size: 48px;
    color: var(--md-sys-color-success);
    margin: 0 auto var(--md-sys-spacing-4);
  }

  h3 {
    font-size: var(--md-sys-typescale-title-large-size);
    font-weight: var(--md-sys-typescale-font-weight-medium);
  }

  p {
    color: var(--md-sys-color-on-surface-variant);
  }
}

// 覆盖Material Dialog样式
:host {
  display: block;

  ::ng-deep .mat-mdc-dialog-container {
    padding: 0 !important;

    .mdc-dialog__surface {
      padding: 0 !important;
    }

    .mat-mdc-dialog-surface {
      padding: 0 !important;
    }

    .mat-mdc-dialog-content {
      padding: 0 !important;
      margin: 0 !important;
      display: block !important;
    }
  }
}

// 响应式调整
@media (max-width: 500px) {
  .two-factor-auth-dialog {
    padding: var(--md-sys-spacing-4);
  }

  .dialog-header {
    margin-bottom: var(--md-sys-spacing-4);

    .dialog-title h2 {
      font-size: var(--md-sys-typescale-title-large-size);
    }
  }

  .recovery-codes {
    grid-template-columns: 1fr;
  }
}
