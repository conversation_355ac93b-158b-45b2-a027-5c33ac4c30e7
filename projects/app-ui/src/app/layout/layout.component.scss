:host {
  --app-toolbar-height: var(--md-sys-layout-header-height);
}

.sidenav-container {
  height: 100%;
}

.sidenav {
  width: var(--md-sys-layout-sidebar-width);
  box-shadow: var(--md-sys-elevation-level2);
  background-color: var(--md-sys-color-surface);
  border-right: none;

  // 移动端默认隐藏
  @media (max-width: 599px) {
    transform: translateX(-100%);
    visibility: hidden;

    &.mat-drawer-opened {
      transform: translateX(0);
      visibility: visible;
    }
  }
}

.mat-sidenav-content {
  background-color: var(--md-sys-color-background);
  min-height: 100vh;
  transition: padding var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

@media (max-width: 599px) {
  :host {
    --app-toolbar-height: var(--md-sys-layout-header-height);
  }

  .sidenav {
    width: var(--md-sys-layout-sidebar-width);
    max-width: 280px;
  }
}
