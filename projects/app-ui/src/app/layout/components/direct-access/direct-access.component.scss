:host {
  display: block;
  position: relative;
}

.direct-access-container {
  max-width: 400px;

  transition: max-width var(--md-sys-motion-duration-medium2);
  display: flex;
  align-items: center;
  position: relative;

  &:focus-within {
    max-width: 450px;
  }

  .direct-access-field {

    ::ng-deep .mat-mdc-form-field-flex {
      align-items: center;
      background-color: rgba(255, 255, 255, 0.15);
      border-radius: 100px;
      transition: background-color var(--md-sys-motion-duration-short2);

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    ::ng-deep .mat-mdc-form-field-infix {
      padding: 8px 0 8px 1em;
      min-height: unset;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      padding: 0 0 0 var(--md-sys-spacing-4);
      background-color: transparent;
    }

    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    ::ng-deep .mdc-notched-outline__leading,
    ::ng-deep .mdc-notched-outline__notch,
    ::ng-deep .mdc-notched-outline__trailing {
      border-color: transparent !important;
    }

    input {
      color: var(--md-sys-color-on-primary);
      caret-color: var(--md-sys-color-on-primary);

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    button {
      color: var(--md-sys-color-on-primary);
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      }
    }
  }

  // 聊天面板样式
  .chat-panel {
    position: absolute;
    top: 50px;
    left: 0;
    width: 350px;
    max-height: 500px;
    background-color: var(--md-sys-color-background);
    border-radius: var(--md-sys-shape-corner-large);
    box-shadow: var(--md-sys-elevation-level3);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    overflow: hidden;
    animation: fadeIn 0.2s ease-in-out;

    .chat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4);
      background-color: var(--md-sys-color-primary);
      color: var(--md-sys-color-on-primary);

      h3 {

        font-size: var(--md-sys-typescale-body-medium-size);
        font-weight: 500;
      }

      button {
        color: var(--md-sys-color-on-primary);
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: var(--md-sys-spacing-4);
      display: flex;
      flex-direction: column;
      gap: var(--md-sys-spacing-2);
      max-height: 350px;

      .message {
        display: flex;
        margin-bottom: var(--md-sys-spacing-2);

        .message-content {
          padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4);
          border-radius: var(--md-sys-shape-corner-large);
          max-width: 80%;

          p {

            white-space: pre-line;
          }

          &.loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--md-sys-spacing-2) var(--md-sys-spacing-6);

            .dot {
              width: 8px;
              height: 8px;
              background-color: var(--md-sys-color-on-surface-variant);
              border-radius: 50%;
              margin: 0 2px;
              animation: bounce 1.5s infinite ease-in-out;

              &:nth-child(1) {
                animation-delay: 0s;
              }

              &:nth-child(2) {
                animation-delay: 0.2s;
              }

              &:nth-child(3) {
                animation-delay: 0.4s;
              }
            }
          }
        }

        &.user-message {
          justify-content: flex-end;

          .message-content {
            background-color: var(--app-primary-light-color);
            color: var(--md-sys-color-on-primary);
          }
        }

        &.system-message {
          justify-content: flex-start;

          .message-content {
            background-color: var(--app-background-alt-color);
            color: var(--md-sys-color-on-surface);
          }
        }

        .message-links {
          display: flex;
          flex-wrap: wrap;
          gap: var(--md-sys-spacing-1);
          margin-top: var(--md-sys-spacing-2);

          a {

            padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
            font-size: var(--md-sys-typescale-label-large-size);
            line-height: 1;
            white-space: nowrap;
          }
        }
      }
    }

    .chat-input {
      padding: var(--md-sys-spacing-2);
      border-top: 1px solid var(--md-sys-color-outline);

      .chat-field {

        ::ng-deep .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }
      }
    }
  }
}

// 移动设备样式
.mobile-direct-access-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn var(--md-sys-motion-duration-short2) forwards;

  .mobile-direct-access-field {

    height: 42px;

    ::ng-deep .mat-mdc-form-field-flex {
      background-color: rgba(255, 255, 255, 0.15);
      border-radius: var(--md-sys-shape-corner-full);
      transition: background-color var(--md-sys-motion-duration-short2);

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    ::ng-deep .mat-mdc-form-field-infix {
      padding: 8px 0 8px 1em;
      min-height: unset;
    }

    ::ng-deep .mat-mdc-text-field-wrapper {
      padding: 0 0 0 var(--md-sys-spacing-4);
      background-color: transparent;
    }

    input {
      color: var(--md-sys-color-on-primary);
      caret-color: var(--md-sys-color-on-primary);
      height: 28px;
      line-height: 28px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    button {
      color: var(--md-sys-color-on-primary);
      height: 42px;
      width: 42px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      }
    }

    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    ::ng-deep .mdc-notched-outline__leading,
    ::ng-deep .mdc-notched-outline__notch,
    ::ng-deep .mdc-notched-outline__trailing {
      border-color: transparent !important;
    }
  }

  // 移动端聊天面板
  .mobile-chat-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    height: 80%;
    max-height: 600px;
    background-color: var(--md-sys-color-background);
    border-radius: var(--md-sys-shape-corner-large);
    display: flex;
    flex-direction: column;
    z-index: 1001;
    overflow: hidden;
    box-shadow: var(--md-sys-elevation-level3);
    animation: zoomIn 0.2s ease-in-out;

    .chat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4);
      background-color: var(--md-sys-color-primary);
      color: var(--md-sys-color-on-primary);

      h3 {

        font-size: var(--md-sys-typescale-body-medium-size);
        font-weight: 500;
      }

      .header-actions {
        display: flex;
        gap: var(--md-sys-spacing-1);
      }

      button {
        color: var(--md-sys-color-on-primary);
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: var(--md-sys-spacing-4);
      display: flex;
      flex-direction: column;
      gap: var(--md-sys-spacing-2);

      // 与桌面版相同的消息样式
      .message {
        display: flex;
        margin-bottom: var(--md-sys-spacing-2);

        .message-content {
          padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4);
          border-radius: var(--md-sys-shape-corner-large);
          max-width: 80%;

          p {

            white-space: pre-line;
          }

          &.loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--md-sys-spacing-2) var(--md-sys-spacing-6);

            .dot {
              width: 8px;
              height: 8px;
              background-color: var(--md-sys-color-on-surface-variant);
              border-radius: 50%;
              margin: 0 2px;
              animation: bounce 1.5s infinite ease-in-out;

              &:nth-child(1) {
                animation-delay: 0s;
              }

              &:nth-child(2) {
                animation-delay: 0.2s;
              }

              &:nth-child(3) {
                animation-delay: 0.4s;
              }
            }
          }
        }

        &.user-message {
          justify-content: flex-end;

          .message-content {
            background-color: var(--app-primary-light-color);
            color: var(--md-sys-color-on-primary);
          }
        }

        &.system-message {
          justify-content: flex-start;

          .message-content {
            background-color: var(--app-background-alt-color);
            color: var(--md-sys-color-on-surface);
          }
        }

        .message-links {
          display: flex;
          flex-wrap: wrap;
          gap: var(--md-sys-spacing-1);
          margin-top: var(--md-sys-spacing-2);

          a {

            padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
            font-size: var(--md-sys-typescale-label-large-size);
            line-height: 1;
            white-space: nowrap;
          }
        }
      }
    }

    .chat-input {
      padding: var(--md-sys-spacing-4);
      border-top: 1px solid var(--md-sys-color-outline);
      background-color: var(--md-sys-color-background);

      .chat-field {

        ::ng-deep .mat-mdc-form-field-flex {
          background-color: var(--app-background-alt-color);
          border-radius: var(--md-sys-shape-corner-full);
        }

        ::ng-deep .mat-mdc-form-field-infix {
          padding: 8px 0 8px 1em;
        }

        ::ng-deep .mat-mdc-text-field-wrapper {
          padding: 0 0 0 var(--md-sys-spacing-4);
        }

        ::ng-deep .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
