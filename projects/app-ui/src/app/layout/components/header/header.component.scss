:host {
  display: block;
}

.mat-toolbar.mat-primary {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: var(--md-sys-elevation-level2);
  height: var(--app-toolbar-height);
  padding: 0 var(--md-sys-spacing-4);
  display: flex;
  justify-content: space-between;

  .toolbar-left, .toolbar-right {
    display: flex;
    align-items: center;
  }

  .toolbar-left {
    gap: 0.5em;
    button {
      mat-icon {
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
      }
    }

    .app-logo {
      display: flex;
      align-items: center;
      margin-right: var(--md-sys-spacing-4);
      transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

      &:hover {
        transform: scale(1.05);
      }

      img {
        display: block;
        width: 36px;
        height: 36px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }
    }

    .app-title {
      font-weight: var(--md-sys-typescale-font-weight-semibold);
      font-size: var(--md-sys-typescale-title-medium-size);
      margin-right: var(--md-sys-spacing-8);
      white-space: nowrap;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  .toolbar-right {
    button {
      margin-left: var(--md-sys-spacing-2);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        transform: scale(0);
        transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
      }

      &:hover::after {
        transform: scale(1);
      }
    }

    mat-icon {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }
  }
}

@media (max-width: 959px) {
  .toolbar-left {
    .app-title {
      font-size: var(--md-sys-typescale-body-medium-size) !important;
      margin-right: var(--md-sys-spacing-4) !important;
    }
  }
}

@media (max-width: 599px) {
  .mat-toolbar.mat-primary {
    padding: 0 var(--md-sys-spacing-2);
    height: var(--app-toolbar-height);

    .app-title {
      font-size: var(--md-sys-typescale-body-medium-size);
      margin-right: 0 !important;
    }

    .toolbar-right button {
      margin-left: var(--md-sys-spacing-1);
    }
  }
}
