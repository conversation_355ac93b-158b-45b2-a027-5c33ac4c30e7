<div class="user-menu-container">
  <button type="button" aria-label="User account" mat-icon-button class="user-avatar-button"
          [matMenuTriggerFor]="userMenu">
    <mat-icon svgIcon="account" matButtonIcon aria-label="User account icon"></mat-icon>
  </button>
</div>

<mat-menu #userMenu="matMenu" class="user-menu">
  <div class="menu-content">
    <!-- 未登录状态 -->
    @if (!isLoggedIn) {
      <div class="menu-header">
        <div class="guest-avatar">
          <mat-icon svgIcon="account" aria-label="Guest avatar"></mat-icon>
        </div>
        <div class="guest-label">访客</div>
      </div>

      <div class="menu-items">
        <button mat-menu-item (click)="openLoginDialog()" class="menu-item">
          <mat-icon svgIcon="login" matListItemIcon aria-label="Login icon"></mat-icon>
          <span>登录</span>
        </button>
        <button mat-menu-item (click)="openRegisterDialog()" class="menu-item">
          <mat-icon svgIcon="person" matListItemIcon aria-label="Register icon"></mat-icon>
          <span>注册</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item [matMenuTriggerFor]="helpMenu" class="menu-item">
          <mat-icon svgIcon="help" aria-label="Help icon"></mat-icon>
          <span>帮助与支持</span>
        </button>
      </div>
    }

    <!-- 已登录状态 -->
    @if (isLoggedIn) {
      <div class="menu-header">
        <div class="user-avatar">
          <mat-icon svgIcon="account" aria-label="User avatar"></mat-icon>
        </div>
        <div class="user-name">{{ userName }}</div>
      </div>

      <div class="menu-items">
        <button mat-menu-item routerLink="/profile" class="menu-item">
          <mat-icon svgIcon="person" aria-label="Profile icon"></mat-icon>
          <span>个人中心</span>
        </button>
        <button mat-menu-item routerLink="/dashboard" class="menu-item">
          <mat-icon svgIcon="dashboard" aria-label="Dashboard icon"></mat-icon>
          <span>控制台</span>
        </button>
        <button mat-menu-item routerLink="/messages" class="menu-item">
          <mat-icon svgIcon="notifications" aria-label="Messages icon"></mat-icon>
          <span>消息中心</span>
        </button>
        <button mat-menu-item routerLink="/favorites" class="menu-item">
          <mat-icon svgIcon="favorite" aria-label="Favorites icon"></mat-icon>
          <span>收藏夹</span>
        </button>
        <button mat-menu-item routerLink="/settings" class="menu-item">
          <mat-icon svgIcon="settings" aria-label="Settings icon"></mat-icon>
          <span>设置</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item [matMenuTriggerFor]="helpMenu" class="menu-item">
          <mat-icon svgIcon="help" aria-label="Help icon"></mat-icon>
          <span>帮助与支持</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="logout()" class="menu-item">
          <mat-icon svgIcon="logout" aria-label="Logout icon"></mat-icon>
          <span>退出登录</span>
        </button>
      </div>
    }
  </div>
</mat-menu>

<!-- 帮助与支持子菜单 -->
<mat-menu #helpMenu="matMenu" class="help-submenu">
  <button mat-menu-item routerLink="/help" class="menu-item">
    <mat-icon svgIcon="help" aria-label="Help center icon"></mat-icon>
    <span>帮助中心</span>
  </button>
  <button mat-menu-item routerLink="/about" class="menu-item">
    <mat-icon svgIcon="info" aria-label="About icon"></mat-icon>
    <span>关于我们</span>
  </button>
  <button mat-menu-item routerLink="/contact" class="menu-item">
    <mat-icon svgIcon="contact" aria-label="Contact icon"></mat-icon>
    <span>联系我们</span>
  </button>
</mat-menu>
