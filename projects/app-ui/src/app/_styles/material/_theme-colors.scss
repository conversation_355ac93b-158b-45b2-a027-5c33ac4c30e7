// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: #0D47A1
$_palettes: (
  primary: (
    0: #000000,
    10: #001945,
    20: #002d6f,
    25: #003785,
    30: #00429c,
    35: #1a4ea8,
    40: #2b5bb5,
    50: #4974d0,
    60: #648eec,
    70: #85a9ff,
    80: #b0c6ff,
    90: #d9e2ff,
    95: #eef0ff,
    98: #faf8ff,
    99: #fefbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #0a1a3b,
    20: #213051,
    25: #2c3b5d,
    30: #384669,
    35: #435275,
    40: #4f5e82,
    50: #68769c,
    60: #8290b7,
    70: #9cabd3,
    80: #b7c6f0,
    90: #d9e2ff,
    95: #eef0ff,
    98: #faf8ff,
    99: #fefbff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #330044,
    20: #510c67,
    25: #5d1c73,
    30: #6a297f,
    35: #77368c,
    40: #854399,
    50: #a05cb4,
    60: #bc76d0,
    70: #d990ed,
    80: #f0b0ff,
    90: #fad7ff,
    95: #ffebff,
    98: #fff7fb,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1a1b21,
    20: #2e3036,
    25: #3a3b42,
    30: #45464d,
    35: #515259,
    40: #5d5e65,
    50: #76777e,
    60: #8f9098,
    70: #aaabb2,
    80: #c6c6ce,
    90: #e2e2ea,
    95: #f0f0f8,
    98: #faf8ff,
    99: #fefbff,
    100: #ffffff,
    4: #0c0e14,
    6: #111319,
    12: #1e1f25,
    17: #282a30,
    22: #33353b,
    24: #37393f,
    87: #d9d9e1,
    92: #e8e7f0,
    94: #ededf5,
    96: #f3f3fb,
  ),
  neutral-variant: (
    0: #000000,
    10: #181b26,
    20: #2c303b,
    25: #383b46,
    30: #434652,
    35: #4f525e,
    40: #5b5e6a,
    50: #737783,
    60: #8d909d,
    70: #a8abb8,
    80: #c3c6d4,
    90: #dfe2f0,
    95: #eef0ff,
    98: #faf8ff,
    99: #fefbff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);
