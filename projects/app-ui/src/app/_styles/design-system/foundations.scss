// 基础样式 (Foundations)
// 定义全局的基础样式，包括重置、排版、链接等

// ===== 全局重置 =====
html, body {
  height: 100%;

  font-family: var(--md-sys-typescale-font-family-base);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-line-height-normal);
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {

}

// ===== 排版系统 =====
// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-family: var(--md-sys-typescale-font-family-heading);
  font-weight: var(--md-sys-typescale-font-weight-medium);
  line-height: var(--md-sys-typescale-line-height-tight);
  margin-top: var(--md-sys-spacing-6);
  margin-bottom: var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);
}

h1 {
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-font-weight-regular);
}

h2 {
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-font-weight-regular);
}

h3 {
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-font-weight-medium);
}

h4 {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-font-weight-medium);
}

h5 {
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: var(--md-sys-typescale-font-weight-medium);
}

h6 {
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: var(--md-sys-typescale-font-weight-medium);
}

// 段落样式
p {
  margin-top: 0;
  margin-bottom: var(--md-sys-spacing-4);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-line-height-normal);
  color: var(--md-sys-color-on-surface);
}

// 列表样式
ul, ol {
  margin: var(--md-sys-spacing-4) 0;
  padding-left: var(--md-sys-spacing-6);
}

li {
  margin: var(--md-sys-spacing-2) 0;
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-line-height-normal);
  color: var(--md-sys-color-on-surface);
}

// 链接样式
a {
  color: var(--md-sys-color-primary);
  text-decoration: none;
  transition: color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  &:hover {
    color: var(--md-sys-color-on-primary-container);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
    border-radius: var(--md-sys-shape-corner-extra-small);
  }
}

// 代码样式
code, pre {
  font-family: var(--md-sys-typescale-font-family-monospace);
  font-size: var(--md-sys-typescale-body-small-size);
}

code {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
  border-radius: var(--md-sys-shape-corner-extra-small);
}

pre {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  padding: var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-small);
  overflow-x: auto;
  margin: var(--md-sys-spacing-4) 0;

  > code {

    background: none;
  }
}

// 引用样式
blockquote {
  margin: var(--md-sys-spacing-4) 0;
  padding: var(--md-sys-spacing-4) var(--md-sys-spacing-6);
  border-left: 4px solid var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  font-style: italic;

  p:last-child {
    margin-bottom: 0;
  }
}

// 分隔线样式
hr {
  border: none;
  height: 1px;
  background-color: var(--md-sys-color-outline-variant);
  margin: var(--md-sys-spacing-6) 0;
}

// 图片样式
img {
  border-radius: var(--md-sys-shape-corner-small);
}

// 表格基础样式
table {

  border-collapse: collapse;
  margin: var(--md-sys-spacing-4) 0;
}

th, td {
  padding: var(--md-sys-spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

th {
  font-weight: var(--md-sys-typescale-font-weight-medium);
  color: var(--md-sys-color-on-surface);
  background-color: var(--md-sys-color-surface-variant);
}

// ===== 实用工具类 =====
// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// 字体粗细
.font-light { font-weight: var(--md-sys-typescale-font-weight-light); }
.font-regular { font-weight: var(--md-sys-typescale-font-weight-regular); }
.font-medium { font-weight: var(--md-sys-typescale-font-weight-medium); }
.font-semibold { font-weight: var(--md-sys-typescale-font-weight-semibold); }
.font-bold { font-weight: var(--md-sys-typescale-font-weight-bold); }

// 文本大小
.text-small { font-size: var(--md-sys-typescale-body-small-size); }
.text-medium { font-size: var(--md-sys-typescale-body-medium-size); }
.text-large { font-size: var(--md-sys-typescale-body-large-size); }

// 颜色工具类
.text-primary { color: var(--md-sys-color-primary); }
.text-secondary { color: var(--md-sys-color-on-surface-variant); }
.text-error { color: var(--md-sys-color-error); }
.text-success { color: var(--md-sys-color-success); }
.text-warning { color: var(--md-sys-color-warning); }

// 背景颜色工具类
.bg-surface { background-color: var(--md-sys-color-surface); }
.bg-surface-variant { background-color: var(--md-sys-color-surface-variant); }
.bg-primary { background-color: var(--md-sys-color-primary); }
.bg-error { background-color: var(--md-sys-color-error); }
.bg-success { background-color: var(--md-sys-color-success); }
.bg-warning { background-color: var(--md-sys-color-warning); }

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: var(--md-sys-spacing-1); }
.m-2 { margin: var(--md-sys-spacing-2); }
.m-3 { margin: var(--md-sys-spacing-3); }
.m-4 { margin: var(--md-sys-spacing-4); }
.m-6 { margin: var(--md-sys-spacing-6); }
.m-8 { margin: var(--md-sys-spacing-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--md-sys-spacing-1); }
.p-2 { padding: var(--md-sys-spacing-2); }
.p-3 { padding: var(--md-sys-spacing-3); }
.p-4 { padding: var(--md-sys-spacing-4); }
.p-6 { padding: var(--md-sys-spacing-6); }
.p-8 { padding: var(--md-sys-spacing-8); }

// 显示工具类
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

.inline-flex { display: inline-flex; }
.grid { display: grid; }

// Flexbox 工具类
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

// 圆角工具类
.rounded-none { border-radius: var(--md-sys-shape-corner-none); }
.rounded-xs { border-radius: var(--md-sys-shape-corner-extra-small); }
.rounded-sm { border-radius: var(--md-sys-shape-corner-small); }
.rounded-md { border-radius: var(--md-sys-shape-corner-medium); }
.rounded-lg { border-radius: var(--md-sys-shape-corner-large); }
.rounded-xl { border-radius: var(--md-sys-shape-corner-extra-large); }
.rounded-full { border-radius: var(--md-sys-shape-corner-full); }

// 阴影工具类
.shadow-none { box-shadow: var(--md-sys-elevation-level0); }
.shadow-sm { box-shadow: var(--md-sys-elevation-level1); }
.shadow-md { box-shadow: var(--md-sys-elevation-level2); }
.shadow-lg { box-shadow: var(--md-sys-elevation-level3); }
.shadow-xl { box-shadow: var(--md-sys-elevation-level4); }
.shadow-2xl { box-shadow: var(--md-sys-elevation-level5); }
