/* highlight.js GitHub 主题 */
@import 'highlight.js/styles/github.css';

/* 自定义 highlight.js 样式 */
.markdown-content {
  pre {
    position: relative;
    background-color: var(--md-sys-color-surface-variant);
    border: 1px solid var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-medium);
    padding: var(--md-sys-spacing-4);
    margin: var(--md-sys-spacing-4) 0;
    overflow-x: auto;
  }

  code {
    font-family: var(--md-sys-typescale-font-family-monospace);
    font-size: var(--md-sys-typescale-body-small-size);
  }

  pre code {
    background-color: transparent;

    border-radius: 0;
    color: var(--md-sys-color-on-surface-variant);
    display: block;
    overflow-x: auto;
    line-height: var(--md-sys-typescale-line-height-normal);
  }

  /* 语言标签样式 */
  pre[data-language]::before {
    content: attr(data-language);
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
    font-size: var(--md-sys-typescale-label-small-size);
    font-family: var(--md-sys-typescale-font-family-base);
    color: var(--md-sys-color-on-surface-variant);
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0 0 var(--md-sys-shape-corner-small) var(--md-sys-shape-corner-small);
    text-transform: uppercase;
  }

  /* 暗色模式适配 */
  .dark-theme & {
    pre {
      background-color: #0d1117;
      border-color: #30363d;
    }

    pre code {
      color: #c9d1d9;
    }

    pre::before {
      color: #8b949e;
      background-color: rgba(255, 255, 255, 0.05);
    }
  }
}

/* 为不同语言添加特定的样式 */
.hljs-keyword {
  color: #d73a49;
}

.hljs-string {
  color: #032f62;
}

.hljs-comment {
  color: #6a737d;
  font-style: italic;
}

.hljs-function {
  color: #6f42c1;
}

.hljs-number {
  color: #005cc5;
}

.hljs-tag {
  color: #22863a;
}

.hljs-attr {
  color: #6f42c1;
}

/* 暗色模式下的语法高亮颜色 */
.dark-theme {
  .hljs-keyword {
    color: #ff7b72;
  }

  .hljs-string {
    color: #a5d6ff;
  }

  .hljs-comment {
    color: #8b949e;
  }

  .hljs-function {
    color: #d2a8ff;
  }

  .hljs-number {
    color: #79c0ff;
  }

  .hljs-tag {
    color: #7ee787;
  }

  .hljs-attr {
    color: #d2a8ff;
  }
}
