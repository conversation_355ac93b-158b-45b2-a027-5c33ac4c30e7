// 代码高亮样式
// 基于 GitHub 主题，适配设计系统颜色

.hljs {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-small);
  padding: var(--md-sys-spacing-4);
  overflow-x: auto;
  font-family: var(--md-sys-typescale-font-family-monospace);
  font-size: var(--md-sys-typescale-body-small-size);
  line-height: var(--md-sys-typescale-line-height-normal);
}

// 代码高亮颜色 - 浅色主题
.hljs-keyword,
.hljs-selector-tag,
.hljs-built_in {
  color: #d73a49;
}

.hljs-string,
.hljs-attr {
  color: #032f62;
}

.hljs-comment,
.hljs-quote {
  color: #6a737d;
  font-style: italic;
}

.hljs-function,
.hljs-title {
  color: #6f42c1;
}

.hljs-number,
.hljs-literal {
  color: #005cc5;
}

.hljs-tag,
.hljs-name {
  color: #22863a;
}

.hljs-variable,
.hljs-template-variable {
  color: #e36209;
}

// 暗色主题覆盖
.dark-theme {
  .hljs {
    background-color: #0d1117;
    color: #c9d1d9;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-built_in {
    color: #ff7b72;
  }

  .hljs-string,
  .hljs-attr {
    color: #a5d6ff;
  }

  .hljs-comment,
  .hljs-quote {
    color: #8b949e;
  }

  .hljs-function,
  .hljs-title {
    color: #d2a8ff;
  }

  .hljs-number,
  .hljs-literal {
    color: #79c0ff;
  }

  .hljs-tag,
  .hljs-name {
    color: #7ee787;
  }

  .hljs-variable,
  .hljs-template-variable {
    color: #ffa657;
  }
}
