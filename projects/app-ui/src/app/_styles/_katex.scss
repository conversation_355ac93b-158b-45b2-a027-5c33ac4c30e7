/* KaTeX 样式 */
@import 'katex/dist/katex.min.css';

/* 自定义 KaTeX 样式 */
.katex-error {
  color: var(--md-sys-color-error);
  background-color: var(--md-sys-color-error-container);
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
  border-radius: var(--md-sys-shape-corner-small);
  font-family: var(--md-sys-typescale-font-family-monospace);
  font-size: var(--md-sys-typescale-body-small-size);
  white-space: pre-wrap;
  word-break: break-word;
}

/* 调整 KaTeX 在 Markdown 中的显示 */
.markdown-content {
  .katex-display {
    margin: var(--md-sys-spacing-4) 0;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .katex {
    font-size: var(--md-sys-typescale-body-medium-size);
  }
}
