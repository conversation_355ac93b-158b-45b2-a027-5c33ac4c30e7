// Markdown 模块样式
// 只定义与基础样式不同的 Markdown 特殊样式

.markdown-content {
  // ===== 标题特殊样式 =====
  h1 {
    border-bottom: 2px solid var(--md-sys-color-outline-variant);
    padding-bottom: var(--md-sys-spacing-2);
  }

  h2 {
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    padding-bottom: var(--md-sys-spacing-1);
  }

  h6 {
    color: var(--md-sys-color-on-surface-variant);
  }

  // ===== 链接特殊样式 =====
  a {
    border-bottom: 1px solid transparent;
    transition: border-bottom-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      border-bottom-color: var(--md-sys-color-primary);
    }

    &:visited {
      color: var(--md-sys-color-on-primary-container);
    }
  }

  // ===== 任务列表特殊样式 =====
  ul.task-list {
    list-style: none;
    padding-left: 0;

    li.task-list-item {
      display: flex;
      align-items: flex-start;
      gap: var(--md-sys-spacing-2);

      input[type="checkbox"] {
        margin-top: 0.2em;
        accent-color: var(--md-sys-color-primary);
      }
    }
  }

  // ===== 代码特殊样式 =====
  pre {
    position: relative;

    // 代码语言标签
    &[data-lang]::before {
      content: attr(data-lang);
      position: absolute;
      top: var(--md-sys-spacing-2);
      right: var(--md-sys-spacing-3);
      font-size: var(--md-sys-typescale-label-small-size);
      color: var(--md-sys-color-on-surface-variant);
      background-color: var(--md-sys-color-surface);
      padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
      border-radius: var(--md-sys-shape-corner-extra-small);
      border: 1px solid var(--md-sys-color-outline-variant);
    }
  }

  // ===== 引用特殊样式 =====
  blockquote {
    border-left: 4px solid var(--md-sys-color-primary);
    background-color: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
    border-radius: 0 var(--md-sys-shape-corner-small) var(--md-sys-shape-corner-small) 0;

    // 嵌套引用
    blockquote {
      border-left-color: var(--md-sys-color-secondary);
      background-color: var(--md-sys-color-secondary-container);
      color: var(--md-sys-color-on-secondary-container);
    }
  }

  // ===== 表格特殊样式 =====
  table {
    border: 1px solid var(--md-sys-color-outline-variant);
    border-radius: var(--md-sys-shape-corner-small);
    overflow: hidden;

    thead {
      background-color: var(--md-sys-color-surface-variant);

      th {
        border-bottom: 2px solid var(--md-sys-color-outline-variant);
        border-right: 1px solid var(--md-sys-color-outline-variant);

        &:last-child {
          border-right: none;
        }
      }
    }

    tbody {
      tr {
        &:nth-child(even) {
          background-color: rgba(var(--md-sys-color-surface-rgb), 0.5);
        }

        &:hover {
          background-color: var(--md-sys-color-primary-container);
        }
      }

      td {
        border-right: 1px solid var(--md-sys-color-outline-variant);

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  // ===== 分隔线特殊样式 =====
  hr {
    height: 2px;
    background: linear-gradient(
      to right,
      transparent,
      var(--md-sys-color-outline-variant),
      transparent
    );
  }

  // ===== 图片特殊样式 =====
  img {
    box-shadow: var(--md-sys-elevation-level1);
  }

}
