// 警告框组件样式
// 定义统一的警告框样式

// ===== 基础警告框样式 =====
.alert {
  display: flex;
  padding: var(--md-sys-spacing-4);
  margin: var(--md-sys-spacing-4) 0;
  border-radius: var(--md-sys-shape-corner-medium);
  border-left: 4px solid;
  box-shadow: var(--md-sys-elevation-level1);

  .alert-icon {
    flex-shrink: 0;
    margin-right: var(--md-sys-spacing-3);

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-medium);
      width: var(--md-sys-layout-icon-size-medium);
      height: var(--md-sys-layout-icon-size-medium);
    }
  }

  .alert-content {
    flex: 1;

    .alert-title {
      font-size: var(--md-sys-typescale-title-medium-size);
      font-weight: var(--md-sys-typescale-font-weight-medium);
      margin: 0 0 var(--md-sys-spacing-2);
    }

    .alert-message {
      font-size: var(--md-sys-typescale-body-medium-size);
      line-height: var(--md-sys-typescale-line-height-normal);

    }
  }

  .alert-actions {
    flex-shrink: 0;
    margin-left: var(--md-sys-spacing-3);
    display: flex;
    gap: var(--md-sys-spacing-2);
  }
}

// ===== 警告框变体 =====
.alert-info {
  border-left-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);

  .alert-icon {
    color: var(--md-sys-color-primary);
  }
}

.alert-success {
  border-left-color: var(--md-sys-color-success);
  background-color: var(--md-sys-color-success-container);
  color: var(--md-sys-color-on-success-container);

  .alert-icon {
    color: var(--md-sys-color-success);
  }
}

.alert-warning {
  border-left-color: var(--md-sys-color-warning);
  background-color: var(--md-sys-color-warning-container);
  color: var(--md-sys-color-on-warning-container);

  .alert-icon {
    color: var(--md-sys-color-warning);
  }
}

.alert-error {
  border-left-color: var(--md-sys-color-error);
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);

  .alert-icon {
    color: var(--md-sys-color-error);
  }
}

// ===== 可关闭的警告框 =====
.alert-dismissible {
  .alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--md-sys-spacing-1);
    border-radius: var(--md-sys-shape-corner-small);
    transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }
}

// ===== 紧凑警告框 =====
.alert-compact {
  padding: var(--md-sys-spacing-3);

  .alert-icon {
    margin-right: var(--md-sys-spacing-2);

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }

  .alert-content {
    .alert-title {
      font-size: var(--md-sys-typescale-body-large-size);
      margin-bottom: var(--md-sys-spacing-1);
    }

    .alert-message {
      font-size: var(--md-sys-typescale-body-small-size);
    }
  }
}

// ===== 内联警告框 =====
.alert-inline {
  display: inline-flex;
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);

  border-radius: var(--md-sys-shape-corner-small);
  border-left-width: 3px;

  .alert-icon {
    margin-right: var(--md-sys-spacing-2);

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }

  .alert-content {
    .alert-message {
      font-size: var(--md-sys-typescale-body-small-size);
    }
  }
}

// ===== Markdown 中的警告框 =====
.markdown-content {
  .admonition {
    display: flex;
    padding: var(--md-sys-spacing-4);
    margin: var(--md-sys-spacing-4) 0;
    border-radius: var(--md-sys-shape-corner-medium);
    border-left: 4px solid;
    box-shadow: var(--md-sys-elevation-level1);

    &.note {
      border-left-color: var(--md-sys-color-primary);
      background-color: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);
    }

    &.tip {
      border-left-color: var(--md-sys-color-success);
      background-color: var(--md-sys-color-success-container);
      color: var(--md-sys-color-on-success-container);
    }

    &.warning {
      border-left-color: var(--md-sys-color-warning);
      background-color: var(--md-sys-color-warning-container);
      color: var(--md-sys-color-on-warning-container);
    }

    &.danger {
      border-left-color: var(--md-sys-color-error);
      background-color: var(--md-sys-color-error-container);
      color: var(--md-sys-color-on-error-container);
    }

    .admonition-title {
      font-size: var(--md-sys-typescale-title-medium-size);
      font-weight: var(--md-sys-typescale-font-weight-medium);
      margin: 0 0 var(--md-sys-spacing-2);
      display: flex;
      align-items: center;
      gap: var(--md-sys-spacing-2);

      mat-icon {
        font-size: var(--md-sys-layout-icon-size-small);
        width: var(--md-sys-layout-icon-size-small);
        height: var(--md-sys-layout-icon-size-small);
      }
    }

    .admonition-content {
      font-size: var(--md-sys-typescale-body-medium-size);
      line-height: var(--md-sys-typescale-line-height-normal);

      p:first-child {
        margin-top: 0;
      }

      p:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// ===== 响应式调整 =====
@media (max-width: 599px) {
  .alert {
    padding: var(--md-sys-spacing-3);

    .alert-icon {
      margin-right: var(--md-sys-spacing-2);
    }

    .alert-actions {
      margin-left: var(--md-sys-spacing-2);
      flex-direction: column;
    }
  }
}
