// 按钮组件样式
// 定义统一的按钮样式和变体

// ===== 基础按钮样式 =====
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4);
  border: none;
  border-radius: var(--md-sys-shape-corner-small);
  font-family: var(--md-sys-typescale-font-family-base);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-font-weight-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  user-select: none;
  white-space: nowrap;

  &:focus {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  // 图标间距
  mat-icon {
    margin-right: var(--md-sys-spacing-2);

    &:last-child {
      margin-right: 0;
      margin-left: var(--md-sys-spacing-2);
    }

    &:only-child {

    }
  }
}

// ===== 按钮变体 =====
// 主要按钮
.btn-primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);

  &:hover:not(:disabled) {
    background-color: var(--md-sys-color-on-primary-container);
    box-shadow: var(--md-sys-elevation-level1);
  }

  &:active:not(:disabled) {
    background-color: var(--md-sys-color-on-primary-container);
    box-shadow: var(--md-sys-elevation-level0);
  }
}

// 次要按钮
.btn-secondary {
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);

  &:hover:not(:disabled) {
    background-color: var(--md-sys-color-on-secondary-container);
    box-shadow: var(--md-sys-elevation-level1);
  }

  &:active:not(:disabled) {
    background-color: var(--md-sys-color-on-secondary-container);
    box-shadow: var(--md-sys-elevation-level0);
  }
}

// 轮廓按钮
.btn-outline {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);

  &:hover:not(:disabled) {
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
    border-color: var(--md-sys-color-primary);
  }

  &:active:not(:disabled) {
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  }
}

// 文本按钮
.btn-text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);

  &:hover:not(:disabled) {
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.08);
  }

  &:active:not(:disabled) {
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.12);
  }
}

// 错误按钮
.btn-error {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);

  &:hover:not(:disabled) {
    background-color: var(--md-sys-color-on-error-container);
    box-shadow: var(--md-sys-elevation-level1);
  }

  &:active:not(:disabled) {
    background-color: var(--md-sys-color-on-error-container);
    box-shadow: var(--md-sys-elevation-level0);
  }
}

// 成功按钮
.btn-success {
  background-color: var(--md-sys-color-success);
  color: var(--md-sys-color-on-success);

  &:hover:not(:disabled) {
    background-color: var(--md-sys-color-on-success-container);
    box-shadow: var(--md-sys-elevation-level1);
  }

  &:active:not(:disabled) {
    background-color: var(--md-sys-color-on-success-container);
    box-shadow: var(--md-sys-elevation-level0);
  }
}

// ===== 按钮尺寸 =====
.btn-small {
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-3);
  font-size: var(--md-sys-typescale-label-medium-size);

  mat-icon {
    font-size: var(--md-sys-layout-icon-size-small);
    width: var(--md-sys-layout-icon-size-small);
    height: var(--md-sys-layout-icon-size-small);
  }
}

.btn-large {
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-6);
  font-size: var(--md-sys-typescale-title-small-size);

  mat-icon {
    font-size: var(--md-sys-layout-icon-size-large);
    width: var(--md-sys-layout-icon-size-large);
    height: var(--md-sys-layout-icon-size-large);
  }
}

// ===== 图标按钮 =====
.btn-icon {
  width: 40px;
  height: 40px;

  border-radius: var(--md-sys-shape-corner-full);

  mat-icon {

    font-size: var(--md-sys-layout-icon-size-medium);
    width: var(--md-sys-layout-icon-size-medium);
    height: var(--md-sys-layout-icon-size-medium);
  }

  &.btn-small {
    width: 32px;
    height: 32px;

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }

  &.btn-large {
    width: 48px;
    height: 48px;

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-large);
      width: var(--md-sys-layout-icon-size-large);
      height: var(--md-sys-layout-icon-size-large);
    }
  }
}

// ===== 浮动操作按钮 =====
.btn-fab {
  width: 56px;
  height: 56px;

  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level3);

  &:hover:not(:disabled) {
    box-shadow: var(--md-sys-elevation-level4);
  }

  &:active:not(:disabled) {
    box-shadow: var(--md-sys-elevation-level2);
  }

  mat-icon {

    font-size: var(--md-sys-layout-icon-size-medium);
    width: var(--md-sys-layout-icon-size-medium);
    height: var(--md-sys-layout-icon-size-medium);
  }

  &.btn-small {
    width: 40px;
    height: 40px;

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-small);
      width: var(--md-sys-layout-icon-size-small);
      height: var(--md-sys-layout-icon-size-small);
    }
  }
}

// ===== 按钮组 =====
.btn-group {
  display: inline-flex;

  .btn {
    border-radius: 0;

    &:first-child {
      border-top-left-radius: var(--md-sys-shape-corner-small);
      border-bottom-left-radius: var(--md-sys-shape-corner-small);
    }

    &:last-child {
      border-top-right-radius: var(--md-sys-shape-corner-small);
      border-bottom-right-radius: var(--md-sys-shape-corner-small);
    }

    &:not(:last-child) {
      border-right: 1px solid var(--md-sys-color-outline-variant);
    }
  }
}

// ===== 切换按钮 =====
.btn-toggle {
  &.active {
    background-color: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
  }
}

// ===== 加载状态按钮 =====
.btn-loading {
  position: relative;
  color: transparent;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid currentColor;
    border-radius: var(--md-sys-shape-corner-full);
    border-top-color: transparent;
    animation: btn-spin 1s linear infinite;
  }
}

@keyframes btn-spin {
  to {
    transform: rotate(360deg);
  }
}

// ===== 响应式调整 =====
@media (max-width: 599px) {
  .btn {
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
    font-size: var(--md-sys-typescale-label-medium-size);
  }

  .btn-large {
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
    font-size: var(--md-sys-typescale-label-large-size);
  }
}
