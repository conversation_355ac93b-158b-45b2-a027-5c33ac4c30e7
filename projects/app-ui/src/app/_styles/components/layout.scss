// 布局组件样式
// 定义统一的布局相关样式

// ===== 容器 =====
.container {

  max-width: var(--md-sys-layout-content-max-width);
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-4);

  &.container-fluid {
    max-width: none;
  }

  &.container-narrow {
    max-width: 800px;
  }

  &.container-wide {
    max-width: 1400px;
  }
}

// ===== 页面布局 =====
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  background-color: var(--md-sys-color-surface);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  padding: var(--md-sys-spacing-4) 0;

  .page-title {
    font-size: var(--md-sys-typescale-headline-medium-size);
    font-weight: var(--md-sys-typescale-font-weight-medium);
    color: var(--md-sys-color-on-surface);

  }

  .page-subtitle {
    font-size: var(--md-sys-typescale-body-large-size);
    color: var(--md-sys-color-on-surface-variant);
    margin: var(--md-sys-spacing-1) 0 0;
  }
}

.page-content {
  flex: 1;
  padding: var(--md-sys-spacing-6) 0;
}

.page-footer {
  background-color: var(--md-sys-color-surface-variant);
  border-top: 1px solid var(--md-sys-color-outline-variant);
  padding: var(--md-sys-spacing-6) 0;
  margin-top: auto;
}

// ===== 侧边栏布局 =====
.sidebar-layout {
  display: flex;
  min-height: 100vh;

  .sidebar {
    width: var(--md-sys-layout-sidebar-width);
    background-color: var(--md-sys-color-surface);
    border-right: 1px solid var(--md-sys-color-outline-variant);
    flex-shrink: 0;

    &.sidebar-collapsed {
      width: 60px;
    }
  }

  .main-content {
    flex: 1;
    background-color: var(--md-sys-color-background);
    overflow-x: hidden;
  }
}

// ===== 导航栏 =====
.navbar {
  height: var(--md-sys-layout-header-height);
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  display: flex;
  align-items: center;
  padding: 0 var(--md-sys-spacing-4);
  box-shadow: var(--md-sys-elevation-level2);

  .navbar-brand {
    font-size: var(--md-sys-typescale-title-large-size);
    font-weight: var(--md-sys-typescale-font-weight-medium);
    color: inherit;
    text-decoration: none;
    margin-right: var(--md-sys-spacing-6);
  }

  .navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-2);
    margin-left: auto;
  }

  .navbar-item {
    color: inherit;
    text-decoration: none;
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
    border-radius: var(--md-sys-shape-corner-small);
    transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

// ===== 面包屑 =====
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  padding: var(--md-sys-spacing-3) 0;

  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--md-sys-spacing-2);

    a {
      color: var(--md-sys-color-primary);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    &:not(:last-child)::after {
      content: '/';
      color: var(--md-sys-color-on-surface-variant);
      margin-left: var(--md-sys-spacing-2);
    }

    &:last-child {
      color: var(--md-sys-color-on-surface-variant);
    }
  }
}

// ===== 网格系统 =====
.grid {
  display: grid;
  gap: var(--md-sys-spacing-4);

  &.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  &.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  &.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  &.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  &.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  &.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  &.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

  &.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  &.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

// ===== Flexbox 布局 =====
.flex-layout {
  display: flex;

  &.flex-col { flex-direction: column; }
  &.flex-row { flex-direction: row; }
  &.flex-wrap { flex-wrap: wrap; }
  &.flex-nowrap { flex-wrap: nowrap; }

  &.items-start { align-items: flex-start; }
  &.items-center { align-items: center; }
  &.items-end { align-items: flex-end; }
  &.items-stretch { align-items: stretch; }

  &.justify-start { justify-content: flex-start; }
  &.justify-center { justify-content: center; }
  &.justify-end { justify-content: flex-end; }
  &.justify-between { justify-content: space-between; }
  &.justify-around { justify-content: space-around; }
  &.justify-evenly { justify-content: space-evenly; }

  &.gap-1 { gap: var(--md-sys-spacing-1); }
  &.gap-2 { gap: var(--md-sys-spacing-2); }
  &.gap-3 { gap: var(--md-sys-spacing-3); }
  &.gap-4 { gap: var(--md-sys-spacing-4); }
  &.gap-6 { gap: var(--md-sys-spacing-6); }
  &.gap-8 { gap: var(--md-sys-spacing-8); }
}

// ===== 分隔符 =====
.divider {
  height: 1px;
  background-color: var(--md-sys-color-outline-variant);
  margin: var(--md-sys-spacing-4) 0;

  &.divider-vertical {
    width: 1px;

    margin: 0 var(--md-sys-spacing-4);
  }

  &.divider-thick {
    height: 2px;
  }
}

// ===== 间距工具 =====
.spacer {
  flex: 1;
}

.spacer-sm { height: var(--md-sys-spacing-2); }
.spacer-md { height: var(--md-sys-spacing-4); }
.spacer-lg { height: var(--md-sys-spacing-6); }
.spacer-xl { height: var(--md-sys-spacing-8); }

// ===== 粘性定位 =====
.sticky-top {
  position: sticky;
  top: 0;
  z-index: var(--md-sys-z-index-sticky);
}

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: var(--md-sys-z-index-sticky);
}

// ===== 响应式调整 =====
@media (max-width: 1024px) {
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--md-sys-spacing-3);
  }

  .sidebar-layout {
    flex-direction: column;

    .sidebar {

      border-right: none;
      border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }
  }

  .navbar {
    padding: 0 var(--md-sys-spacing-3);

    .navbar-brand {
      margin-right: var(--md-sys-spacing-3);
    }
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(2, 1fr);
  }

  .flex-layout.flex-row {
    flex-direction: column;
  }
}

@media (max-width: 599px) {
  .page-content {
    padding: var(--md-sys-spacing-4) 0;
  }

  .page-header {
    padding: var(--md-sys-spacing-3) 0;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6,
  .grid-cols-12 {
    grid-template-columns: 1fr;
  }

  .navbar {
    height: var(--md-sys-layout-header-height);

    .navbar-nav {
      gap: var(--md-sys-spacing-1);
    }
  }
}
