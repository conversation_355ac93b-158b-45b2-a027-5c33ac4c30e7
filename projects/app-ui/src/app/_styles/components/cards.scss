// 卡片组件样式
// 定义统一的卡片样式和变体

// ===== 基础卡片样式 =====
.card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-shape-corner-medium);
  box-shadow: var(--md-sys-elevation-level1);
  overflow: hidden;
  transition: box-shadow var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  &:hover {
    box-shadow: var(--md-sys-elevation-level2);
  }
}

// ===== 卡片变体 =====
// 轮廓卡片
.card-outlined {
  box-shadow: none;
  border: 1px solid var(--md-sys-color-outline-variant);

  &:hover {
    border-color: var(--md-sys-color-outline);
    box-shadow: var(--md-sys-elevation-level1);
  }
}

// 填充卡片
.card-filled {
  background-color: var(--md-sys-color-surface-variant);
  box-shadow: none;

  &:hover {
    box-shadow: var(--md-sys-elevation-level1);
  }
}

// 高程卡片
.card-elevated {
  box-shadow: var(--md-sys-elevation-level3);

  &:hover {
    box-shadow: var(--md-sys-elevation-level4);
  }
}

// ===== 卡片内容区域 =====
.card-header {
  padding: var(--md-sys-spacing-4) var(--md-sys-spacing-4) 0;

  .card-title {
    font-size: var(--md-sys-typescale-title-large-size);
    font-weight: var(--md-sys-typescale-font-weight-medium);
    line-height: var(--md-sys-typescale-line-height-tight);
    color: var(--md-sys-color-on-surface);
    margin: 0 0 var(--md-sys-spacing-1);
  }

  .card-subtitle {
    font-size: var(--md-sys-typescale-title-medium-size);
    font-weight: var(--md-sys-typescale-font-weight-regular);
    color: var(--md-sys-color-on-surface-variant);

  }
}

.card-content {
  padding: var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);

  p:first-child {
    margin-top: 0;
  }

  p:last-child {
    margin-bottom: 0;
  }
}

.card-actions {
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4) var(--md-sys-spacing-4);
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);

  &.card-actions-align-end {
    justify-content: flex-end;
  }

  &.card-actions-align-center {
    justify-content: center;
  }

  &.card-actions-align-between {
    justify-content: space-between;
  }
}

.card-media {

  height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  img {

    height: 100%;
    object-fit: cover;
    display: block;
  }

  &.card-media-small {
    height: 120px;
  }

  &.card-media-large {
    height: 300px;
  }
}

// ===== 特殊卡片类型 =====
// 可点击卡片
.card-clickable {
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--md-sys-elevation-level3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: var(--md-sys-elevation-level1);
  }
}

// 信息卡片
.card-info {
  border-left: 4px solid var(--md-sys-color-primary);

  .card-header .card-title {
    color: var(--md-sys-color-primary);
  }
}

// 成功卡片
.card-success {
  border-left: 4px solid var(--md-sys-color-success);

  .card-header .card-title {
    color: var(--md-sys-color-success);
  }
}

// 警告卡片
.card-warning {
  border-left: 4px solid var(--md-sys-color-warning);

  .card-header .card-title {
    color: var(--md-sys-color-warning);
  }
}

// 错误卡片
.card-error {
  border-left: 4px solid var(--md-sys-color-error);

  .card-header .card-title {
    color: var(--md-sys-color-error);
  }
}

// ===== 卡片布局 =====
// 水平卡片
.card-horizontal {
  display: flex;

  .card-media {
    width: 200px;

    flex-shrink: 0;
  }

  .card-content-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .card-content {
    flex: 1;
  }
}

// 紧凑卡片
.card-compact {
  .card-header {
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-3) 0;

    .card-title {
      font-size: var(--md-sys-typescale-title-medium-size);
    }

    .card-subtitle {
      font-size: var(--md-sys-typescale-body-medium-size);
    }
  }

  .card-content {
    padding: var(--md-sys-spacing-3);
  }

  .card-actions {
    padding: var(--md-sys-spacing-1) var(--md-sys-spacing-3) var(--md-sys-spacing-3);
  }
}

// ===== 卡片网格 =====
.card-grid {
  display: grid;
  gap: var(--md-sys-spacing-4);

  &.card-grid-1 {
    grid-template-columns: 1fr;
  }

  &.card-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  &.card-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  &.card-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  &.card-grid-auto {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

// ===== 统计卡片 =====
.card-stat {
  text-align: center;

  .stat-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto var(--md-sys-spacing-3);
    background-color: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
    border-radius: var(--md-sys-shape-corner-full);
    display: flex;
    align-items: center;
    justify-content: center;

    mat-icon {
      font-size: var(--md-sys-layout-icon-size-medium);
      width: var(--md-sys-layout-icon-size-medium);
      height: var(--md-sys-layout-icon-size-medium);
    }
  }

  .stat-value {
    font-size: var(--md-sys-typescale-headline-medium-size);
    font-weight: var(--md-sys-typescale-font-weight-bold);
    color: var(--md-sys-color-on-surface);
    margin: 0 0 var(--md-sys-spacing-1);
  }

  .stat-label {
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface-variant);

  }
}

// ===== 响应式调整 =====
@media (max-width: 768px) {
  .card-grid-2,
  .card-grid-3,
  .card-grid-4 {
    grid-template-columns: 1fr;
  }

  .card-horizontal {
    flex-direction: column;

    .card-media {

      height: 200px;
    }
  }
}

@media (max-width: 599px) {
  .card-header {
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-3) 0;
  }

  .card-content {
    padding: var(--md-sys-spacing-3);
  }

  .card-actions {
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3) var(--md-sys-spacing-3);
  }
}
