:host {
  display: block;
}

.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

.profile-header {
  margin-bottom: var(--md-sys-spacing-6);
}

.profile-info {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--md-sys-spacing-6);
  padding: var(--md-sys-spacing-4) 0;
}

.profile-avatar {
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--app-primary-light-color);
  }
}

.profile-details {
  h1 {
    margin: 0 0 var(--md-sys-spacing-2) 0;
    color: var(--md-sys-color-on-surface);
  }

  .profile-bio {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }

  .profile-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--md-sys-spacing-4);

    .meta-item {
      display: flex;
      align-items: center;
      color: var(--md-sys-color-on-surface-variant);

      .mat-icon {
        margin-right: var(--md-sys-spacing-1);
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  padding: var(--md-sys-spacing-4) 0;

  .stat-item {
    text-align: center;

    .stat-value {
      font-size: var(--md-sys-typescale-headline-small-size);
      font-weight: 500;
      color: var(--md-sys-color-primary);
    }

    .stat-label {
      color: var(--md-sys-color-on-surface-variant);
    }
  }
}

.profile-content {
  .tab-content {
    padding: var(--md-sys-spacing-4) 0;
  }

  .activity-list {
    .activity-item {
      display: flex;
      align-items: flex-start;
      padding: var(--md-sys-spacing-4);
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);

      &:last-child {
        border-bottom: none;
      }

      .activity-icon {
        margin-right: var(--md-sys-spacing-4);

        .mat-icon {
          color: var(--md-sys-color-primary);
        }
      }

      .activity-details {
        flex: 1;

        .activity-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--md-sys-spacing-1);

          .activity-type {
            font-weight: 500;
          }

          .activity-time {
            color: var(--md-sys-color-on-surface-variant);
            font-size: var(--md-sys-typescale-label-large-size);
          }
        }

        .activity-title {
          color: var(--md-sys-color-on-surface);
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    color: var(--md-sys-color-on-surface-variant);
    padding: var(--md-sys-spacing-8) 0;
  }
}

@media (max-width: 768px) {
  .profile-info {
    grid-template-columns: 1fr;
    text-align: center;

    .profile-avatar {
      margin: 0 auto;
    }

    .profile-meta {
      justify-content: center;
    }

    .profile-actions {
      margin-top: var(--md-sys-spacing-4);
    }
  }
}
