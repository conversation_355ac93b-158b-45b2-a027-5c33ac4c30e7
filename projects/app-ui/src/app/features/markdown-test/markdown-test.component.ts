import { Component } from '@angular/core';
import { MarkdownComponent } from '../../shared/markdown/markdown.component';

@Component({
  selector: 'app-markdown-test',
  standalone: true,
  imports: [MarkdownComponent],
  templateUrl: './markdown-test.component.html',
  styleUrls: ['./markdown-test.component.scss']
})
export class MarkdownTestComponent {
  testContent = `# Markdown 测试页面

这是一个用于测试 Markdown 组件渲染效果的页面。

## 基本文本格式

这是一个**粗体文本**和*斜体文本*的测试。

这是一个~~删除线文本~~的测试。

这是一个\`内联代码\`的测试。

## 标题测试

# 一级标题
## 二级标题  
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

## 列表测试

### 无序列表
- 项目1
- 项目2
  - 子项目1
  - 子项目2
    - 子子项目1
    - 子子项目2
- 项目3

### 有序列表
1. 第一项
2. 第二项
   1. 子项目1
   2. 子项目2
3. 第三项

## 链接和图片测试

[这是一个链接](https://example.com)

[这是一个带标题的链接](https://example.com "链接标题")

![图片描述](https://via.placeholder.com/300x200 "图片标题")

## 引用测试

> 这是一个简单的引用块

> 这是一个多行的引用块
> 包含多行内容
> 可以有很多行

> 这是一个嵌套的引用块
> > 这是嵌套的内容
> > 可以有多层嵌套

## 代码块测试

### JavaScript 代码
\`\`\`javascript
function hello() {
  console.log("Hello World!");
  return "success";
}

const data = {
  name: "测试",
  value: 123
};
\`\`\`

### Python 代码
\`\`\`python
def hello():
    print("Hello World!")
    return "success"

data = {
    "name": "测试",
    "value": 123
}
\`\`\`

### HTML 代码
\`\`\`html
<div class="container">
  <h1>标题</h1>
  <p>这是一个段落</p>
</div>
\`\`\`

## 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |
| 长数据内容 | 更长的数据内容 | 非常长的数据内容测试 |

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:-------:|-------:|
| 左 | 中 | 右 |
| 测试 | 测试 | 测试 |

## 分隔线测试

---

***

___

## 数学公式测试

内联数学公式：$E = mc^2$

块级数学公式：
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

## Mermaid 图表测试

\`\`\`mermaid
graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[显示登录页]
    C --> E[结束]
    D --> E
\`\`\`

## 特殊内容测试

### 提及测试
@用户名 @测试用户

### 复选框测试
- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务

### 警告框测试
!!! note "注意"
    这是一个注意事项

!!! warning "警告"
    这是一个警告信息

!!! danger "危险"
    这是一个危险警告

## 混合内容测试

这是一个包含**粗体**、*斜体*、\`代码\`和[链接](https://example.com)的复杂段落。

> 这是一个引用块，包含**粗体**文本和\`代码\`
> 
> 还可以包含列表：
> - 项目1
> - 项目2

\`\`\`javascript
// 这是代码块中的注释
function complexFunction() {
  const data = {
    name: "测试",
    items: [1, 2, 3]
  };
  return data;
}
\`\`\`
`;
}
