:host {
  display: block;
  padding: var(--md-sys-spacing-6);
  max-width: 1200px;
  margin: 0 auto;
}

.markdown-test-container {
  .header {
    margin-bottom: var(--md-sys-spacing-8);
    text-align: center;
    
    h1 {
      color: var(--md-sys-color-primary);
      margin-bottom: var(--md-sys-spacing-4);
    }
    
    p {
      color: var(--md-sys-color-on-surface-variant);
      font-size: var(--md-sys-typescale-body-large-size);
    }
  }
  
  .content {
    background-color: var(--md-sys-color-surface-container-low);
    border-radius: var(--md-sys-shape-corner-large);
    padding: var(--md-sys-spacing-6);
    box-shadow: var(--md-sys-elevation-level1);
  }
}
