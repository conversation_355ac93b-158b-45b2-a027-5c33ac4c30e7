:host {
  display: block;
}

.favorites-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

h1 {
  margin-bottom: var(--md-sys-spacing-6);
  color: var(--md-sys-color-primary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

.favorites-list {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

.favorite-item {
  transition: transform var(--md-sys-motion-duration-short2), box-shadow var(--md-sys-motion-duration-short2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
  }
}

.favorite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-2);
}

.favorite-type {
  display: flex;
  align-items: center;
  font-size: var(--md-sys-typescale-label-large-size);
  padding: 4px 8px;
  border-radius: var(--md-sys-shape-corner-small);

  &.article {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
  }

  &.video {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
  }

  .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.favorite-title {
  margin: var(--md-sys-spacing-2) 0;
  font-size: var(--md-sys-typescale-title-medium-size);
  color: var(--md-sys-color-on-surface);
}

.favorite-description {
  margin-bottom: var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface-variant);
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .meta-info {
    display: flex;
    align-items: center;
    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-label-large-size);

    .meta-author {
      margin-right: var(--md-sys-spacing-2);
      font-weight: 500;
    }
  }

  .favorite-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--md-sys-spacing-1);

    mat-chip-option {
      --mat-chip-container-height: 24px;
      font-size: var(--md-sys-typescale-label-small-size);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;
  color: var(--md-sys-color-on-surface-variant);

  .mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: var(--md-sys-spacing-4);
    opacity: 0.5;
  }

  p {
    margin-bottom: var(--md-sys-spacing-4);
    font-size: var(--md-sys-typescale-body-medium-size);
  }
}

@media (max-width: 599px) {
  .favorite-meta {
    flex-direction: column;
    align-items: flex-start;

    .meta-info {
      margin-bottom: var(--md-sys-spacing-2);
    }
  }
}
