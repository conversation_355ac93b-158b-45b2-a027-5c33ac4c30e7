:host {
  display: block;
}

.help-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

h1 {
  margin-bottom: var(--md-sys-spacing-6);
  color: var(--md-sys-color-primary);
}

h2 {
  margin-bottom: var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
  padding: var(--md-sys-spacing-8) 0;
}

.help-search {
  margin-bottom: var(--md-sys-spacing-8);

}

.help-categories {
  margin-bottom: var(--md-sys-spacing-8);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--md-sys-spacing-4);
}

.category-card {
  height: 100%;
  transition: box-shadow var(--md-sys-motion-duration-short2), transform var(--md-sys-motion-duration-short2), border-color var(--md-sys-motion-duration-short2);
  cursor: pointer;
  border: 2px solid transparent;

  &:hover {
    box-shadow: var(--shadow-lg);
  }

  &.selected {
    border-color: var(--md-sys-color-primary);
    background-color: rgba(25, 118, 210, 0.05);

    .category-icon {
      background-color: rgba(25, 118, 210, 0.2);
    }

    h3 {
      color: var(--md-sys-color-primary);
    }
  }

  .category-icon {
    background-color: rgba(25, 118, 210, 0.1);
    margin-bottom: var(--md-sys-spacing-4);

    .mat-icon {
      color: var(--md-sys-color-primary);
    }
  }

  h3 {
    margin: 0 0 var(--md-sys-spacing-2) 0;
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface);
  }

  p {

    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-body-small-size);
  }
}

.active-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--md-sys-spacing-4);
  padding: var(--md-sys-spacing-2);
  background-color: rgba(25, 118, 210, 0.05);
  border-radius: var(--md-sys-shape-corner-small);

  span {
    margin-right: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);

    strong {
      color: var(--md-sys-color-primary);
    }
  }
}

.help-faqs {
  margin-bottom: var(--md-sys-spacing-8);

  mat-expansion-panel {
    margin-bottom: var(--md-sys-spacing-1);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--md-sys-spacing-8) 0;
    color: var(--md-sys-color-on-surface-variant);

    .mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: var(--md-sys-spacing-4);
      opacity: 0.5;
    }

    p {
      margin-bottom: var(--md-sys-spacing-4);
    }
  }
}

.help-contact {
  .contact-info {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .contact-text {
      flex: 1;

      p {

        color: var(--md-sys-color-on-surface-variant);
      }

      .support-email {
        margin-top: var(--md-sys-spacing-2);
        font-weight: 500;
        color: var(--md-sys-color-primary);
      }
    }

    .contact-actions {
      display: flex;
      gap: var(--md-sys-spacing-4);

      button, a {
        .mat-icon {
          margin-right: var(--md-sys-spacing-1);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .help-contact {
    .contact-info {
      flex-direction: column;
      align-items: flex-start;

      .contact-text {
        margin-bottom: var(--md-sys-spacing-4);
      }

      .contact-actions {

        flex-direction: column;

        button, a {

          margin-bottom: var(--md-sys-spacing-1);
        }
      }
    }
  }
}
