:host {
  display: block;
  height: 100%;
}

.settings-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);

  overflow: hidden; /* 防止内容溢出导致滚动条 */
}

h1 {
  margin-bottom: var(--md-sys-spacing-6);
  color: var(--md-sys-color-primary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

h2 {
  margin: var(--md-sys-spacing-4) 0;
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-title-medium-size);
}

.tab-content {
  padding: var(--md-sys-spacing-4) 0;
  overflow: hidden; /* 防止内容溢出导致滚动条 */
}

/* 确保标签内容不会导致滚动条 */
::ng-deep .mat-mdc-tab-body-wrapper {
  overflow: hidden;
}

/* 移除mat-tab-body上的滚动条 */
::ng-deep .mat-mdc-tab-body {
  overflow-y: hidden !important;
}

::ng-deep .mat-mdc-tab-body-content {
  overflow: hidden !important;
}

.settings-form {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--md-sys-spacing-4);
  margin-bottom: var(--md-sys-spacing-6);
}

.settings-actions {
  display: flex;
  gap: var(--md-sys-spacing-4);
  margin: var(--md-sys-spacing-4) 0;

  button {
    .mat-icon {
      margin-right: var(--md-sys-spacing-1);
    }
  }
}

.settings-list {
  .settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--md-sys-spacing-4) 0;

    .settings-item-info {
      flex: 1;

      h3 {
        margin: 0 0 var(--md-sys-spacing-1) 0;
        font-size: var(--md-sys-typescale-body-medium-size);
        color: var(--md-sys-color-on-surface);
      }

      p {

        color: var(--md-sys-color-on-surface-variant);
        font-size: var(--md-sys-typescale-label-large-size);
      }
    }

    mat-form-field {
      width: 200px;
    }
  }

  mat-divider {

  }
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--md-sys-spacing-6);

  button {
    min-width: 120px;

    mat-spinner {
      display: inline-block;
      margin-right: var(--md-sys-spacing-1);
    }

    .button-text {
      vertical-align: middle;
    }

    mat-icon {
      margin-right: var(--md-sys-spacing-1);
    }
  }
}

/* 确保卡片内容不会导致滚动条 */
::ng-deep .mat-mdc-card-content {
  overflow: visible;
}

@media (max-width: 768px) {
  .settings-form {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    flex-direction: column;

    button {

    }
  }

  .settings-list {
    .settings-item {
      flex-direction: column;
      align-items: flex-start;

      .settings-item-info {
        margin-bottom: var(--md-sys-spacing-2);
      }

      mat-form-field, mat-slide-toggle {
        align-self: flex-start;

      }
    }
  }
}
