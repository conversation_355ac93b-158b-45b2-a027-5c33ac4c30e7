:host {
  display: block;
}

.about-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

.about-header {
  text-align: center;
  margin-bottom: var(--md-sys-spacing-8);

  h1 {
    margin-bottom: var(--md-sys-spacing-2);
    color: var(--md-sys-color-primary);
  }

  .subtitle {
    font-size: var(--md-sys-typescale-title-medium-size);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
  padding: var(--md-sys-spacing-8) 0;
}

.about-section {
  margin-bottom: var(--md-sys-spacing-8);

  h2 {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface);
    font-size: var(--md-sys-typescale-headline-small-size);
  }
}

.company-info {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: var(--md-sys-spacing-6);

  .company-text {
    h3 {
      margin: var(--md-sys-spacing-4) 0 var(--md-sys-spacing-2);
      color: var(--md-sys-color-primary);
    }

    p {
      margin: 0 0 var(--md-sys-spacing-4) 0;
      color: var(--md-sys-color-on-surface-variant);
      line-height: 1.6;
    }
  }

  .company-image {
    img {

      height: 100%;
      object-fit: cover;
      border-radius: var(--md-sys-shape-corner-medium);
    }
  }
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--md-sys-spacing-4);
}

.team-card {
  text-align: center;

  .member-avatar {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--md-sys-spacing-4);

    img {

      height: 100%;
      object-fit: cover;
      border-radius: 50%;
      border: 3px solid var(--app-primary-light-color);
    }
  }

  h3 {
    margin: 0 0 var(--md-sys-spacing-1) 0;
    color: var(--md-sys-color-on-surface);
  }

  .member-title {
    color: var(--md-sys-color-primary);
    font-weight: 500;
    margin-bottom: var(--md-sys-spacing-2);
  }

  .member-bio {
    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-label-large-size);

  }
}

.timeline {
  position: relative;
  padding: var(--md-sys-spacing-4) 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--app-primary-light-color);
  }

  .timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: var(--md-sys-spacing-6);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .timeline-badge {
    position: absolute;
    left: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--md-sys-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }

  .timeline-content {
    h3 {
      margin: 0 0 var(--md-sys-spacing-1) 0;
      color: var(--md-sys-color-on-surface);
    }

    p {

      color: var(--md-sys-color-on-surface-variant);
    }
  }
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--md-sys-spacing-4);
}

.value-card {
  text-align: center;

  .mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: var(--md-sys-color-primary);
    margin-bottom: var(--md-sys-spacing-2);
  }

  h3 {
    margin: 0 0 var(--md-sys-spacing-2) 0;
    color: var(--md-sys-color-on-surface);
  }

  p {

    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-label-large-size);

  }
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--md-sys-spacing-6);
  margin-bottom: var(--md-sys-spacing-6);
}

.contact-item {
  display: flex;
  align-items: flex-start;

  .mat-icon {
    color: var(--md-sys-color-primary);
    margin-right: var(--md-sys-spacing-2);
  }

  .contact-text {
    h3 {
      margin: 0 0 var(--md-sys-spacing-1) 0;
      font-size: var(--md-sys-typescale-body-medium-size);
      color: var(--md-sys-color-on-surface);
    }

    p {

      color: var(--md-sys-color-on-surface-variant);
    }
  }
}

.contact-actions {
  display: flex;
  justify-content: center;

  button {
    .mat-icon {
      margin-right: var(--md-sys-spacing-1);
    }
  }
}

@media (max-width: 768px) {
  .company-info {
    grid-template-columns: 1fr;

    .company-image {
      order: -1;
      margin-bottom: var(--md-sys-spacing-4);
    }
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-4);
  }
}
