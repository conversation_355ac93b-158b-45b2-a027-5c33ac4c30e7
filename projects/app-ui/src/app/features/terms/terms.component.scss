.terms-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.terms-card {
  margin-bottom: 24px;
}

mat-card-header {
  margin-bottom: 16px;
}

mat-card-content {
  section {
    margin: 24px 0;

    h2 {
      font-size: 1.5rem;
      margin-bottom: 16px;
      color: var(--md-sys-color-primary);
    }

    p {
      margin-bottom: 12px;
      line-height: 1.6;
    }

    ul {
      margin-left: 24px;
      margin-bottom: 16px;

      li {
        margin-bottom: 8px;

      }
    }
  }
}

mat-divider {
  margin: 24px 0;
}

mat-card-actions {
  padding: 16px;
  display: flex;
  justify-content: flex-end;

  .privacy-link {
    color: var(--md-sys-color-primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 响应式调整
@media (max-width: 599px) {
  .terms-container {
    padding: 16px;
  }

  mat-card-content {
    section {
      margin: 16px 0;

      h2 {
        font-size: 1.3rem;
      }
    }
  }

  mat-divider {
    margin: 16px 0;
  }
}
