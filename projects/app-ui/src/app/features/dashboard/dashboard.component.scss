:host {
  display: block;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

h1 {
  margin-bottom: var(--md-sys-spacing-6);
  color: var(--md-sys-color-primary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
    text-align: center;
  }
}

.empty-state {
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
  padding: var(--md-sys-spacing-4) 0;
}

h2 {
  margin-bottom: var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: var(--md-sys-spacing-4);
  margin-bottom: var(--md-sys-spacing-6);
}

.stat-card {
  display: flex;
  align-items: center;
  padding: var(--md-sys-spacing-4);

  .icon-thumb {
    margin-bottom: var(--md-sys-spacing-2);
  }
}

.stat-content {
  text-align: center;

  h3 {
    margin: var(--md-sys-spacing-1) 0;
    font-size: var(--md-sys-typescale-body-medium-size);
    color: var(--md-sys-color-on-surface-variant);
  }

  .stat-value {
    font-size: var(--md-sys-typescale-headline-small-size);
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
  }
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--md-sys-spacing-6);
}

.activity-list {
  .activity-item {
    display: flex;
    padding: var(--md-sys-spacing-2) 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);

    &:last-child {
      border-bottom: none;
    }

    .activity-time {
      min-width: 80px;
      color: var(--md-sys-color-on-surface-variant);
      font-size: var(--md-sys-typescale-label-large-size);
    }

    .activity-details {
      h4 {
        margin: 0 0 var(--md-sys-spacing-1) 0;
        font-size: var(--md-sys-typescale-body-medium-size);
      }

      p {

        color: var(--md-sys-color-on-surface-variant);
      }
    }
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);

  button {
    height: 48px;
    justify-content: flex-start;
    font-size: var(--md-sys-typescale-body-medium-size);
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}
