@if (message.role === 'assistant') {
  <div class="generation-status-container" [class.completed]="!message.isGenerating"
       [class.expanded]="expanded">
    <div class="generation-status-header" (click)="toggleExpanded()" (keydown.enter)="toggleExpanded()" tabindex="0"
         role="button">
      <!-- 生成中状态显示脉动点 -->
      @if (message.isGenerating) {
        <div class="status-indicator">
          <div class="pulse-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
      }

      <!-- 已完成状态显示图标 -->
      @if (!message.isGenerating) {
        <div class="status-icon">
          <div class="gemini-icon"></div>
        </div>
      }

      <!-- 状态文本 -->
      <div class="status-text">
        {{ activeStepName }}
      </div>

      <!-- 展开/折叠按钮 -->
      <button mat-icon-button class="toggle-button" aria-label="展开或折叠思路">
        @if (!expanded) {
          <mat-icon svgIcon="expand_more"></mat-icon>
        } @else {
          <mat-icon svgIcon="expand_less"></mat-icon>
        }
      </button>
    </div>

    <!-- 详细信息区域 -->
    @if (expanded) {
      <div class="generation-status-details">
        <!-- 按阶段分组显示 -->
        @if (phaseGroups.length > 0) {
          <div class="phase-groups">
            @for (group of phaseGroups; track group.phase) {
              <div class="phase-group">
                <!-- 阶段标题 -->
                <div class="phase-header">
                  <div class="phase-name">{{ group.phaseName }}</div>
                </div>

                <!-- 阶段内的步骤列表 -->
                <div class="steps-list">
                  @for (step of group.steps; track step.id; let i = $index) {
                    <div class="step-item"
                         [class.completed]="step.completed"
                         [class.active]="!step.completed && isActiveStep(step)">
                      <div class="step-header">
                        <div class="step-dot"></div>
                        <div class="step-name">
                          {{ step.name }}
                          @if (step.duration) {
                            <div class="step-duration">{{ step.duration }}ms</div>
                          }
                        </div>
                      </div>

                      <!-- 步骤输出内容 -->
                      @if (step.content) {
                        <div class="step-output">
                          <div class="output-content">{{ step.content }}</div>
                        </div>
                      }
                    </div>
                  }
                </div>
              </div>
            }
          </div>
        }

        <!-- 如果没有步骤，显示提示 -->
        @if (steps.length === 0) {
          <div class="no-steps-message">
            暂无思路信息
          </div>
        }
      </div>
    }
  </div>
}
