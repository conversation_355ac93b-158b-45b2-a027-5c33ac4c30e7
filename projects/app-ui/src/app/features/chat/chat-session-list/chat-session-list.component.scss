.session-lists {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.session-list-section {
  margin-bottom: var(--md-sys-spacing-4);

  .section-title {
    font-size: var(--md-sys-typescale-label-large-size);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin: var(--md-sys-spacing-1) var(--md-sys-spacing-2);

  }

  &.pinned-sessions {
    margin-bottom: var(--md-sys-spacing-2);

    // 置顶会话区域的特殊样式
    .section-title {
      color: var(--md-sys-color-primary);
      font-weight: var(--font-weight-bold);
    }

    // 置顶图标指示器
    .action-buttons {
      display: flex;
      align-items: center;
      gap: var(--md-sys-spacing-1);

      .pin-indicator {
        color: var(--md-sys-color-primary);
        font-size: 20px;
        height: 24px;
        width: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

/* 会话项的样式已移至 session.component.scss */

/* 自定义会话列表样式 */
.new-chat-buttons {
  display: flex;
  justify-content: space-between;
  padding: var(--md-sys-spacing-6) var(--md-sys-spacing-4) var(--md-sys-spacing-4);
  margin-bottom: var(--md-sys-spacing-4);

  button {
    flex: 1;
    margin: 0 var(--md-sys-spacing-1);

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

:host {
  display: block;
  height: 100%;
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(var(--primary-rgb), 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--primary-rgb), 0.5);
  }
}

// 修复 Material 列表项样式
::ng-deep {
  .mat-mdc-list-item {
    // 调整图标对齐
    .mdc-list-item__start {
      margin-top: 0;
      align-self: center;
    }

    // 调整文本样式
    .mdc-list-item__primary-text {
      display: block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-size: var(--font-size-base);
    }
  }
}
