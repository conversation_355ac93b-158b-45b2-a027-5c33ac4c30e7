.message-container {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--md-sys-spacing-4);

  .message-row {
    display: flex;
    align-items: flex-start;
  }

  &.user-message {
    .message-row {
      flex-direction: row-reverse;
    }

    .message-content {
      background-color: #1677ff; // 蓝色系气泡
      color: #ffffff; // 白色文字
      border-radius: 6px; // 小圆角矩形
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); // 轻微阴影
      position: relative;
      margin-right: 8px;

      // 添加更圆润的小三角形
      &:after {
        content: '';
        position: absolute;
        right: -5px; // 增加稍微的偏移量使其更明显
        top: 15px; // 调整位置使其指向头像中间
        width: 10px; // 增加小三角形的大小
        height: 10px;
        background-color: #1677ff;
        transform: rotate(45deg);
        border-radius: 2px; // 增加圆角半径
        box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.05); // 与气泡一致的阴影
      }
    }
  }

  &.assistant-message {
    // 添加鼠标悬停效果
    &:hover {
      .message-actions.assistant-actions {
        opacity: 0.7; // 鼠标悬停时恢复正常透明度

        &:hover {
          opacity: 1; // 鼠标悬停在工具条上时完全不透明
        }
      }
    }

    .message-content {
      background-color: var(--md-sys-color-surface); // 浅灰色背景
      color: #000000;
      border-radius: 6px; // 小圆角矩形
      border: none;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); // 轻微阴影
      position: relative;
      margin-left: 8px; // 留出头像的空间

      // 添加更圆润的小三角形
      &:before {
        content: '';
        position: absolute;
        left: -5px; // 增加稍微的偏移量使其更明显
        top: 15px; // 调整位置使其指向头像中间
        width: 10px; // 增加小三角形的大小
        height: 10px;
        background-color: var(--md-sys-color-surface);
        transform: rotate(45deg);
        border-radius: 2px; // 增加圆角半径
        box-shadow: -1px 1px 1px rgba(0, 0, 0, 0.05); // 与气泡一致的阴影
      }
    }
  }
}

.avatar {
  width: 32px;
  height: 32px;
  margin: 0 8px;
  flex-shrink: 0;

  .user-avatar, .assistant-avatar {

    height: 100%;
    border-radius: 4px; // 圆角方形头像
    overflow: hidden; // 确保图片不超出圆角
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); // 轻微的阴影
  }

  .avatar-image {

    height: 100%;
    object-fit: cover; // 确保图片正确填充容器
    display: block;
  }

  // 备用样式，当图片加载失败时显示背景色
  .user-avatar {
    background-color: var(--md-sys-color-surface);
  }

  .assistant-avatar {
    background-color: var(--md-sys-color-surface);
  }
}

.message-content {
  padding: 10px 12px; // 微信气泡内边距
  max-width: 70%; // 微信气泡最大宽度
  transition: none; // 移除过渡效果
  position: relative; // 为了定位消息头部

  &:hover {
    box-shadow: none; // 移除悬停效果，微信没有这个
  }

  // 生成状态包装器
  .generation-status-wrapper {
    margin-bottom: 0;
    margin-top: -4px; // 调整与消息气泡的距离
  }
}

// 消息头部样式
.message-header {
  position: absolute;
  top: -22px;
  left: 0;
  display: flex;
  align-items: center;
  gap: 8px;

  .message-sender {
    font-size: 12px;
    color: var(--md-sys-color-on-surface-variant);

    opacity: 0.8;
  }

  // 版本切换导航样式
  .version-navigation {
    display: flex;
    align-items: center;
    margin-left: 8px;

    .version-button {
      min-width: 24px;
      height: 24px;

      margin: 0 2px;
      border-radius: 4px;
      background-color: var(--md-sys-color-surface);
      color: var(--md-sys-color-on-surface-variant);
      font-size: 12px;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      border: 1px solid var(--md-sys-color-outline);
      transition: all 0.2s ease;

      &.active {
        background-color: var(--md-sys-color-primary);
        color: white;
        border-color: var(--md-sys-color-primary);
      }

      &:hover:not(.active) {
        background-color: var(--md-sys-color-surface-variant);
      }

      &.more-versions {
        font-size: 10px;
      }
    }
  }
}

.message-body {
  word-break: break-word;
  line-height: 1.4; // 微信的行高
  font-size: 16px; // 微信的字体大小

  // 消息生成中的样式
  &.generating {
    opacity: 0.5;
  }

  // 使用微信的文本样式
  ::ng-deep {
    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 代码块样式
    pre {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      padding: 8px;
      margin: 8px 0;
      overflow-x: auto;
    }

    // 链接样式
    a {
      color: var(--link-color);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 附件列表样式
  .message-attachments {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;

    .attachment-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 8px;
      background-color: rgba(0, 0, 0, 0.05);
      text-decoration: none;
      color: inherit;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.08);
      }

      .attachment-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        .attachment-preview {

          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }

        .file-icon {

          height: 100%;
          background-color: var(--md-sys-color-primary);
          color: white;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .attachment-info {
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .attachment-name {
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .attachment-size {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }
    }
  }

  // 消息操作按钮样式
  .message-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
    opacity: 0.7;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }

    // 助理消息的工具条向左对齐
    &.assistant-actions {
      justify-content: flex-start;
      opacity: 0.3; // 默认更淡
    }

    // 修复 Material 图标按钮内部对齐
    ::ng-deep .mat-mdc-icon-button.mat-mdc-button-base {

      display: flex;
      align-items: center;
      justify-content: center;
    }

    ::ng-deep .mat-mdc-icon-button .mdc-button__icon {
      display: flex;
      align-items: center;
      justify-content: center;

    }

    button {
      width: 24px;
      height: 24px;
      line-height: 24px;
      margin-left: 4px;

      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
        line-height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &.active {
        color: var(--md-sys-color-primary);
      }
    }
  }
}
