.suggestion-templates-container {
  margin: var(--md-sys-spacing-4) 0;
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  background-color: rgba(var(--md-sys-color-primary-rgb), 0.04);
  border-radius: var(--md-sys-shape-corner-small);
  border: 1px solid rgba(var(--md-sys-color-primary-rgb), 0.12);
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);

  @media (max-width: 768px) {
    margin: 0 var(--md-sys-spacing-1);
    padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
  }

  &.collapsed {
    padding-bottom: var(--md-sys-spacing-2);

    .collapsed-buttons {
      margin-top: var(--md-sys-spacing-2);
    }
  }
}

// 标题行样式 - 包含标题和折叠状态下的按钮
.suggestion-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 1;
  padding: var(--md-sys-spacing-1) 0;
  border-radius: var(--md-sys-shape-corner-extra-small);
  transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);

  &:hover {
    background-color: rgba(var(--md-sys-color-primary-rgb), 0.04);

    .templates-title {
      color: var(--md-sys-color-primary);
    }
  }

  &:focus-visible {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
  }

  @media (max-width: 768px) {
    gap: var(--md-sys-spacing-1);
  }
}

// 标题和按钮容器
.suggestion-content-wrapper {
  display: flex;
  flex: 1;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-2);
  overflow: hidden;
}

.templates-title {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  font-weight: var(--md-sys-typescale-font-weight-medium);
  transition: color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

// 折叠/展开图标样式
.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  min-width: 24px; // 确保图标不会被压缩
  color: var(--text-color-secondary, #666);
  transition: transform 0.3s ease, color 0.2s ease;
  margin-left: 8px;
  position: relative; // 添加相对定位
  z-index: 2; // 确保在伪元素之上

  &:hover {
    color: var(--md-sys-color-primary);
  }
}

// 内容区域样式
.suggestion-content {
  transition: max-height 0.3s ease, opacity 0.3s ease;
  margin-top: 8px;
}

.templates-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    gap: 6px;
    margin-bottom: 8px;
  }
}

.loading-placeholder,
.empty-placeholder {

  text-align: center;
  padding: 8px;
  color: var(--text-color-secondary, #666);
  font-size: var(--font-size-sm, 14px);
  font-style: italic;
}

.template-button {
  font-size: var(--font-size-sm, 14px);
  background-color: white;
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  border-radius: 16px;
  padding: 4px 12px;

  white-space: normal;
  text-align: left;

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
  }

  @media (max-width: 768px) {
    font-size: 13px;
    padding: 3px 10px;
    border-radius: 14px;
  }
}

/* 报告部分样式 */
.report-section {
  margin-top: 16px;
}

.report-section-title {
  font-size: var(--font-size-sm, 14px);
  color: var(--text-color-secondary, #666);
  margin-bottom: 12px;
  font-weight: 500;
}

.report-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.report-card {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid rgba(var(--primary-rgb), 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.08);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.report-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  min-width: 40px;
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: 50%;
  margin-right: 12px;
  color: var(--md-sys-color-primary);

  svg {
    width: 24px;
    height: 24px;
  }
}

.report-content {
  flex: 1;
  overflow: hidden;
}

.report-title {
  font-size: var(--font-size-md, 16px);
  font-weight: 500;
  color: var(--text-color, #333);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.report-description {
  font-size: var(--font-size-sm, 13px);
  color: var(--text-color-secondary, #666);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2; // 添加标准属性以实现兼容性
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 折叠状态下的缩略按钮样式 - 内联版本 */
.collapsed-buttons-inline {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  pointer-events: none;

  > * {
    pointer-events: all;
  }
  .collapsed-suggestions, .collapsed-reports {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  @media (max-width: 768px) {
    gap: 4px;

    .collapsed-suggestions, .collapsed-reports {
      gap: 4px;
    }
  }
}

.collapsed-button {
  font-size: 12px;
  line-height: 24px;
  height: 28px;
  padding: 0 10px;
  border-radius: 14px;
  min-width: auto;
  white-space: nowrap;
  text-align: center;
  margin: 0 2px;
  position: relative; // 添加相对定位
  z-index: 3; // 确保在其他元素之上

  @media (max-width: 768px) {
    font-size: 11px;
    line-height: 22px;
    height: 24px;
    padding: 0 8px;
    border-radius: 12px;
  }

  &.suggestion-button {
    background-color: rgba(var(--primary-rgb), 0.08);
    color: var(--md-sys-color-primary);
    border: 1px solid rgba(var(--primary-rgb), 0.2);

    &:hover {
      background-color: rgba(var(--primary-rgb), 0.12);
    }
  }

  &.report-button {
    background-color: white;
    color: var(--md-sys-color-primary);
    border: 1px solid rgba(var(--primary-rgb), 0.3);
    display: inline-flex;
    align-items: center;
    flex-direction: row;

    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      display: inline-flex;
      vertical-align: middle;
      margin-right: 4px;
    }

    span {
      line-height: 1;
      vertical-align: middle;
    }

    &:hover {
      background-color: rgba(var(--primary-rgb), 0.08);
    }
  }

  &.more-button {
    background-color: rgba(var(--primary-rgb), 0.05);
    color: var(--text-color-secondary);
    border: 1px dashed rgba(var(--primary-rgb), 0.2);

    &:hover {
      background-color: rgba(var(--primary-rgb), 0.1);
      color: var(--md-sys-color-primary);
    }
  }
}
