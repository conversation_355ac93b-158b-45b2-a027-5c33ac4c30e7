:host {
  display: block;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

.contact-header {
  text-align: center;
  margin-bottom: var(--md-sys-spacing-8);

  h1 {
    margin-bottom: var(--md-sys-spacing-2);
    color: var(--md-sys-color-primary);
  }

  .subtitle {
    font-size: var(--md-sys-typescale-title-medium-size);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  p {
    margin-top: var(--md-sys-spacing-4);
    color: var(--md-sys-color-on-surface-variant);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8) 0;

  .error-message {
    margin-bottom: var(--md-sys-spacing-4);
    color: var(--md-sys-color-error);
  }
}

.contact-content {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: var(--md-sys-spacing-6);
  margin-bottom: var(--md-sys-spacing-8);
}

h2 {
  margin-bottom: var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-title-medium-size);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--md-sys-spacing-4);
  margin-bottom: var(--md-sys-spacing-2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--md-sys-spacing-4);
  margin-top: var(--md-sys-spacing-4);

  button {
    .mat-icon {
      margin-right: var(--md-sys-spacing-1);
    }
  }
}

// 提交状态样式
.submit-success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-medium);
  margin: var(--md-sys-spacing-4) 0;

  p {

  }
}

.submit-error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-medium);
  margin: var(--md-sys-spacing-4) 0;

  p {

  }
}

// 提交按钮中的加载动画
button[type="submit"] {
  min-width: 100px;

  mat-spinner {
    display: inline-block;
    margin-right: var(--md-sys-spacing-2);
    vertical-align: middle;
  }
}

.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-6);
}

.contact-info-list {
  .contact-info-item {
    display: flex;
    align-items: flex-start;
    padding: var(--md-sys-spacing-4) 0;

    .info-icon {
      margin-right: var(--md-sys-spacing-4);

      .mat-icon {
        color: var(--md-sys-color-primary);
      }
    }

    .info-content {
      h3 {
        margin: 0 0 var(--md-sys-spacing-1) 0;
        font-size: var(--md-sys-typescale-body-medium-size);
        color: var(--md-sys-color-on-surface);
      }

      p {

        color: var(--md-sys-color-on-surface-variant);
      }
    }
  }
}

.social-title {
  margin: var(--md-sys-spacing-4) 0 var(--md-sys-spacing-2);
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
}

.social-links {
  display: flex;
  gap: var(--md-sys-spacing-4);

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--md-sys-color-primary);
    transition: background-color var(--md-sys-motion-duration-short2), box-shadow var(--md-sys-motion-duration-short2);

    &:hover {
      background-color: var(--md-sys-color-primary);
      color: white;
      box-shadow: var(--shadow-md);
    }
  }
}

.map-card {
  .map-container {
    height: 200px;
    overflow: hidden;
    border-radius: var(--md-sys-shape-corner-medium);

    img {

      height: 100%;
      object-fit: cover;
    }
  }
}

.wechat-card {
  .wechat-container {
    display: flex;
    justify-content: center;
    padding: var(--md-sys-spacing-4) 0;

    img {
      width: 200px;
      height: 200px;
      object-fit: contain;
    }
  }
}

.contact-faq {
  h2 {
    text-align: center;
    margin-bottom: var(--md-sys-spacing-6);
  }
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--md-sys-spacing-4);
}

.faq-card {
  height: 100%;

  h3 {
    margin: 0 0 var(--md-sys-spacing-2) 0;
    color: var(--md-sys-color-primary);
    font-size: var(--md-sys-typescale-body-medium-size);
  }

  p {

    color: var(--md-sys-color-on-surface-variant);
    font-size: var(--md-sys-typescale-label-large-size);

  }
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}
