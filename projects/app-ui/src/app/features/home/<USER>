:host {
  display: block;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--md-sys-spacing-4);
}

h1 {
  margin-bottom: var(--md-sys-spacing-6);
  color: var(--md-sys-color-primary);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--md-sys-spacing-4);
}

mat-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  mat-card-content {
    flex-grow: 1;
  }
}

@media (max-width: 599px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}
